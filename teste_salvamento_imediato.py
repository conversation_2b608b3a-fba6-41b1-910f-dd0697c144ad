#!/usr/bin/env python3
"""
Teste do salvamento imediato de trades
Demonstra que o arquivo é criado assim que o trade é executado
"""

import sys
import os
import json
import asyncio
from datetime import datetime
import uuid

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def simular_trade_record():
    """Simula criação de um trade record"""
    try:
        from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem, TradingSignal, TradeResult
        
        print("[TEST] TESTE DE SALVAMENTO IMEDIATO")
        print("=" * 60)
        
        # Inicializar sistema
        print("1. Inicializando sistema QUALIA...")
        system = QualiaBinanceCorrectedSystem()
        
        # Verificar se arquivo existe antes
        log_file = system.trade_logger.trades_log_file
        print(f"2. Arquivo de log: {log_file}")
        
        if os.path.exists(log_file):
            print(f"   ⚠️ Arquivo já existe")
            # Fazer backup
            backup_file = f"{log_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(log_file, backup_file)
            print(f"   [BACKUP] Backup criado: {backup_file}")
        else:
            print(f"   [INFO] Arquivo não existe (será criado)")
        
        # Simular dados de mercado
        market_data = {
            'symbol': 'ETH/USDT',
            'price': 3593.34,
            'ohlcv_df': None,  # Simplificado para teste
            'volume_24h': 1250000,
            'spread': 0.001
        }
        
        # Simular sinal de trading
        signal = TradingSignal(
            symbol='ETH/USDT',
            direction='buy',
            entry_price=3593.34,
            target_price=3620.58,
            stop_price=3576.48,
            position_size_usd=21.0,
            confidence_score=0.85,
            quantum_metrics={
                'consciousness': 0.935,
                'coherence': 0.995,
                'confidence': 0.900,
                'volume_surge': 3.089,
                'momentum': 2.140,
                'stability_score': 0.690,
                'predictive_score': 0.832,
                'advanced_quality_score': 1.0,
                'liquidity_score': 0.999,
                'execution_quality': 0.997,
                'momentum_quality': 1.0
            },
            timestamp=datetime.now(),
            signal_type='momentum'
        )
        
        # Simular resultado de trade
        trade_result = TradeResult(
            signal=signal,
            order_id=f"test_{uuid.uuid4().hex[:8]}",
            executed_price=3590.5,
            executed_quantity=0.0058,
            fees_paid=0.0,
            outcome='pending',
            pnl=0.0,
            execution_time=datetime.now()
        )
        
        print("3. Criando trade record...")
        print(f"   Symbol: {signal.symbol}")
        print(f"   Direction: {signal.direction}")
        print(f"   Order ID: {trade_result.order_id}")
        
        # Criar registro (deve salvar imediatamente)
        trade_record = system.trade_logger.create_trade_record(
            signal, trade_result, market_data
        )
        
        print("4. Verificando salvamento...")
        
        # Verificar se arquivo foi criado
        if os.path.exists(log_file):
            print(f"   ✅ Arquivo criado: {log_file}")
            
            # Verificar conteúdo
            with open(log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"   [STATS] Trades finalizados: {len(data.get('trades', []))}")
            print(f"   [ACTIVE] Trades ativos: {len(data.get('active_trades', []))}")
            print(f"   [TIME] Última atualização: {data.get('last_updated', 'N/A')}")
            
            # Mostrar trade ativo
            if data.get('active_trades'):
                active_trade = data['active_trades'][0]
                record = active_trade['trade_record']
                print(f"   [TRADE] Trade ativo registrado:")
                print(f"      - ID: {record['trade_id']}")
                print(f"      - Symbol: {record['symbol']}")
                print(f"      - Status: {record['metadata']['status']}")
                print(f"      - Order ID: {active_trade['order_id']}")
            
            print("   ✅ SALVAMENTO IMEDIATO FUNCIONANDO!")
            
        else:
            print(f"   ❌ Arquivo não foi criado!")
            return False
        
        print("\n5. Testando recuperação...")
        
        # Criar novo sistema para testar recuperação
        system2 = QualiaBinanceCorrectedSystem()
        
        if system2.trade_logger.active_trade_records:
            print(f"   ✅ Trades ativos recuperados: {len(system2.trade_logger.active_trade_records)}")
            for order_id, record in system2.trade_logger.active_trade_records.items():
                print(f"      - {record.symbol} {record.direction} (Order: {order_id})")
        else:
            print(f"   ❌ Nenhum trade ativo recuperado")
            return False
        
        print("\n✅ TESTE COMPLETO: SALVAMENTO IMEDIATO FUNCIONANDO!")
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def verificar_arquivo_existente():
    """Verifica se já existe arquivo de log"""
    log_file = 'qualia_trades_log.json'
    
    print("\n[CHECK] VERIFICAÇÃO DE ARQUIVO EXISTENTE")
    print("=" * 60)
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"[FILE] Arquivo encontrado: {log_file}")
            print(f"[STATS] Trades finalizados: {len(data.get('trades', []))}")
            print(f"[ACTIVE] Trades ativos: {len(data.get('active_trades', []))}")
            print(f"[TIME] Última atualização: {data.get('last_updated', 'N/A')}")
            
            # Mostrar tamanho do arquivo
            size_kb = os.path.getsize(log_file) / 1024
            print(f"[SIZE] Tamanho: {size_kb:.1f} KB")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro lendo arquivo: {e}")
            return False
    else:
        print(f"[INFO] Arquivo não existe: {log_file}")
        print("   (Será criado automaticamente no primeiro trade)")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE DE SALVAMENTO IMEDIATO DE TRADES QUALIA")
    print("=" * 70)
    
    # Verificar arquivo existente
    arquivo_existe = verificar_arquivo_existente()
    
    # Executar teste
    sucesso = simular_trade_record()
    
    if sucesso:
        print("\n🎉 RESULTADO:")
        print("✅ Arquivo é criado IMEDIATAMENTE quando trade é executado")
        print("✅ Trades ativos são salvos para recuperação")
        print("✅ Sistema recupera trades ativos na inicialização")
        print("✅ Não há risco de perda de dados por interrupção")
        
        print("\n[BENEFITS] BENEFÍCIOS:")
        print("   • Proteção contra perda de dados")
        print("   • Recuperação automática de trades ativos")
        print("   • Histórico preservado mesmo com erros")
        print("   • Continuidade garantida após reinicialização")
        
    else:
        print("\n❌ TESTE FALHOU")
        print("   Verifique os logs de erro acima")

if __name__ == "__main__":
    main()
