#!/usr/bin/env python3
"""
Teste de Validação do Sistema de Execução Múltipla
Simula os dados dos logs fornecidos para validar as melhorias implementadas
"""

import asyncio
import sys
import os
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Optional

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

@dataclass
class MockTradingSignal:
    """Mock do TradingSignal para testes"""
    symbol: str
    direction: str
    confidence_score: float
    position_size_usd: float
    entry_price: float
    target_price: float
    stop_price: float
    quantum_metrics: Dict

@dataclass
class MockTradeResult:
    """Mock do TradeResult para testes"""
    signal: MockTradingSignal
    order_id: str
    executed_price: float
    executed_quantity: float
    pnl: float = 0.0
    outcome: str = 'pending'

class MockQualiaBinanceSystem:
    """Mock do sistema QUALIA para testes"""
    
    def __init__(self):
        self.balance_usdt = 126.13
        self.active_trades = {}
        self.completed_trades = []
        
        # Configurações de risco
        self.risk_limits = {
            'max_concurrent_trades': 6,
            'max_exposure_pct': 0.30,
            'max_signals_per_cycle': 3,
            'emergency_stop': False
        }
        
        # Correlações de ativos
        self.asset_correlations = {
            'BTC/USDT': ['ETH/USDT'],
            'ETH/USDT': ['BTC/USDT'],
            'ADA/USDT': ['DOT/USDT'],
            'DOT/USDT': ['ADA/USDT'],
            'SOL/USDT': ['AVAX/USDT'],
            'AVAX/USDT': ['SOL/USDT']
        }
        
        # Estatísticas de execução múltipla
        self.multiple_execution_stats = {
            'total_opportunities': 0,
            'executed_signals': 0,
            'skipped_capital': 0,
            'skipped_correlation': 0,
            'skipped_exposure': 0
        }

    def has_sufficient_capital(self, signal: MockTradingSignal) -> bool:
        """Verifica se há capital suficiente"""
        current_exposure = sum(trade.signal.position_size_usd for trade in self.active_trades.values())
        total_exposure = current_exposure + signal.position_size_usd
        max_exposure = self.balance_usdt * self.risk_limits['max_exposure_pct']
        
        if total_exposure > max_exposure:
            self.multiple_execution_stats['skipped_exposure'] += 1
            return False
        
        if signal.position_size_usd > self.balance_usdt:
            self.multiple_execution_stats['skipped_capital'] += 1
            return False
            
        return True

    def check_asset_correlation(self, signal: MockTradingSignal, active_signals: List[MockTradingSignal]) -> bool:
        """Verifica correlação entre ativos"""
        symbol = signal.symbol
        correlated_assets = self.asset_correlations.get(symbol, [])
        
        for active_signal in active_signals:
            if active_signal.symbol in correlated_assets:
                if active_signal.direction == signal.direction:
                    self.multiple_execution_stats['skipped_correlation'] += 1
                    return False
        
        return True

    async def execute_trade_signal(self, signal: MockTradingSignal) -> Optional[MockTradeResult]:
        """Simula execução de trade"""
        order_id = f"test_{int(datetime.now().timestamp())}"
        
        trade_result = MockTradeResult(
            signal=signal,
            order_id=order_id,
            executed_price=signal.entry_price,
            executed_quantity=signal.position_size_usd / signal.entry_price
        )
        
        # Simular sucesso
        self.active_trades[order_id] = trade_result
        self.balance_usdt -= signal.position_size_usd
        
        print(f"✅ Trade simulado executado: {signal.symbol} ${signal.position_size_usd:.2f}")
        return trade_result

    async def execute_multiple_signals(self, signals: List[MockTradingSignal]) -> List[Optional[MockTradeResult]]:
        """Executa múltiplos sinais com validações"""
        if not signals:
            return []
        
        self.multiple_execution_stats['total_opportunities'] = len(signals)
        
        # Limitar número de sinais
        max_signals = min(len(signals), self.risk_limits['max_signals_per_cycle'])
        signals_to_process = signals[:max_signals]
        
        # Verificar slots disponíveis
        current_active_trades = len(self.active_trades)
        available_slots = self.risk_limits['max_concurrent_trades'] - current_active_trades
        
        if available_slots <= 0:
            print(f"⚠️ Limite de trades simultâneos atingido")
            return []
        
        signals_to_process = signals_to_process[:available_slots]
        
        executed_trades = []
        active_signals = [trade.signal for trade in self.active_trades.values()]
        
        # Balanceamento BUY/SELL CORRIGIDO
        buy_signals = [s for s in signals_to_process if s.direction == 'buy']
        sell_signals = [s for s in signals_to_process if s.direction == 'sell']

        total_signals = len(signals_to_process)

        if total_signals <= max_signals:
            # Executar todos os sinais disponíveis
            balanced_signals = signals_to_process.copy()
            print(f"📊 Executando todos os {total_signals} sinais (≤ limite de {max_signals})")
        else:
            # Aplicar balanceamento apenas quando há excesso
            balanced_signals = []
            max_per_direction = max(1, max_signals // 2)

            for i in range(max(len(buy_signals), len(sell_signals))):
                if i < len(buy_signals) and len([s for s in balanced_signals if s.direction == 'buy']) < max_per_direction:
                    balanced_signals.append(buy_signals[i])
                if i < len(sell_signals) and len([s for s in balanced_signals if s.direction == 'sell']) < max_per_direction:
                    balanced_signals.append(sell_signals[i])
                if len(balanced_signals) >= max_signals:
                    break

            print(f"📊 Balanceamento aplicado: {len(balanced_signals)} de {total_signals} sinais")
        
        print(f"🎯 EXECUÇÃO MÚLTIPLA: {len(balanced_signals)} sinais selecionados")
        print(f"   BUY: {len([s for s in balanced_signals if s.direction == 'buy'])}")
        print(f"   SELL: {len([s for s in balanced_signals if s.direction == 'sell'])}")
        
        # Executar sinais
        for i, signal in enumerate(balanced_signals):
            if not self.has_sufficient_capital(signal):
                print(f"⚠️ Capital insuficiente para {signal.symbol}")
                continue
            
            if not self.check_asset_correlation(signal, active_signals):
                continue
            
            trade_result = await self.execute_trade_signal(signal)
            
            if trade_result:
                executed_trades.append(trade_result)
                active_signals.append(signal)
                self.multiple_execution_stats['executed_signals'] += 1
                
                # Timeout simulado
                if i < len(balanced_signals) - 1:
                    await asyncio.sleep(0.1)  # Reduzido para teste
        
        return executed_trades

def create_test_signals_from_logs():
    """Cria sinais de teste baseados nos logs fornecidos"""
    signals = []
    
    # ETH/USDT - BUY (Confidence: 0.683, Quality: 0.686)
    eth_signal = MockTradingSignal(
        symbol='ETH/USDT',
        direction='buy',
        confidence_score=0.683,
        position_size_usd=10.0,
        entry_price=3500.0,
        target_price=3528.0,  # +0.8%
        stop_price=3486.0,    # -0.4%
        quantum_metrics={
            'consciousness': 0.675,
            'coherence': 0.718,
            'confidence': 0.666,
            'advanced_quality_score': 0.683
        }
    )
    signals.append(eth_signal)
    
    # ADA/USDT - BUY (Confidence: 0.810, Quality: 0.728)
    ada_signal = MockTradingSignal(
        symbol='ADA/USDT',
        direction='buy',
        confidence_score=0.810,
        position_size_usd=10.0,
        entry_price=0.7434,
        target_price=0.7493,  # +0.8%
        stop_price=0.7404,    # -0.4%
        quantum_metrics={
            'consciousness': 0.753,
            'coherence': 0.715,
            'confidence': 0.716,
            'advanced_quality_score': 0.810
        }
    )
    signals.append(ada_signal)
    
    # LINK/USDT - SELL (Confidence: 0.644, Quality: 0.761)
    link_signal = MockTradingSignal(
        symbol='LINK/USDT',
        direction='sell',
        confidence_score=0.644,
        position_size_usd=10.0,
        entry_price=15.50,
        target_price=15.376,  # -0.8% (SELL target é menor)
        stop_price=15.562,    # +0.4% (SELL stop é maior)
        quantum_metrics={
            'consciousness': 0.684,
            'coherence': 0.996,
            'confidence': 0.604,
            'advanced_quality_score': 0.644
        }
    )
    signals.append(link_signal)
    
    return signals

async def test_multiple_execution():
    """Testa o sistema de execução múltipla"""
    print("🧪 TESTE DE VALIDAÇÃO - SISTEMA DE EXECUÇÃO MÚLTIPLA")
    print("=" * 60)
    
    # Criar sistema mock
    system = MockQualiaBinanceSystem()
    
    # Criar sinais baseados nos logs
    signals = create_test_signals_from_logs()
    
    print(f"📊 CENÁRIO DE TESTE:")
    print(f"   Balance inicial: ${system.balance_usdt:.2f}")
    print(f"   Sinais encontrados: {len(signals)}")
    print(f"   Sistema anterior: executaria apenas 1 sinal (ADA/USDT)")
    print(f"   Sistema novo: deve executar até 3 sinais")
    print()
    
    # Executar teste
    executed_trades = await system.execute_multiple_signals(signals)
    
    # Resultados
    print("=" * 60)
    print("📊 RESULTADOS DO TESTE")
    print("=" * 60)
    print(f"✅ Trades executados: {len(executed_trades)}")
    print(f"💰 Capital utilizado: ${sum(t.signal.position_size_usd for t in executed_trades):.2f}")
    print(f"💰 Balance restante: ${system.balance_usdt:.2f}")
    
    # Estatísticas
    stats = system.multiple_execution_stats
    print(f"📈 Taxa de aproveitamento: {(stats['executed_signals']/stats['total_opportunities'])*100:.1f}%")
    print(f"⚠️ Rejeitados por capital: {stats['skipped_capital']}")
    print(f"⚠️ Rejeitados por correlação: {stats['skipped_correlation']}")
    print(f"⚠️ Rejeitados por exposição: {stats['skipped_exposure']}")
    
    # Análise de diversificação
    buy_count = len([t for t in executed_trades if t.signal.direction == 'buy'])
    sell_count = len([t for t in executed_trades if t.signal.direction == 'sell'])
    
    print(f"📊 Diversificação:")
    print(f"   BUY: {buy_count}")
    print(f"   SELL: {sell_count}")
    
    # Validação
    print("=" * 60)
    print("✅ VALIDAÇÕES")
    print("=" * 60)
    
    # Teste 1: Mais trades executados que sistema anterior
    if len(executed_trades) > 1:
        print("✅ PASS: Executa mais trades que sistema anterior (1)")
    else:
        print("❌ FAIL: Não melhorou aproveitamento de oportunidades")
    
    # Teste 2: Gestão de risco mantida
    total_exposure = sum(t.signal.position_size_usd for t in executed_trades)
    initial_balance = 126.13  # Balance inicial antes das execuções
    max_exposure = initial_balance * 0.30
    exposure_pct = (total_exposure / initial_balance) * 100

    print(f"📊 Análise de Exposição:")
    print(f"   Total utilizado: ${total_exposure:.2f}")
    print(f"   Limite máximo: ${max_exposure:.2f} (30%)")
    print(f"   Exposição atual: {exposure_pct:.1f}%")

    if total_exposure <= max_exposure:
        print("✅ PASS: Gestão de risco mantida (exposição ≤ 30%)")
    else:
        print("❌ FAIL: Exposição excede limite de risco")
    
    # Teste 3: Diversificação implementada
    if buy_count > 0 and sell_count > 0:
        print("✅ PASS: Diversificação BUY/SELL implementada")
    else:
        print("⚠️ WARN: Diversificação limitada (apenas uma direção)")
    
    # Teste 4: Correlação verificada
    symbols_executed = [t.signal.symbol for t in executed_trades]
    if 'ETH/USDT' in symbols_executed and 'BTC/USDT' in symbols_executed:
        print("❌ FAIL: Ativos correlacionados executados simultaneamente")
    else:
        print("✅ PASS: Correlação de ativos verificada")
    
    print("=" * 60)
    print("🎉 TESTE CONCLUÍDO")

if __name__ == "__main__":
    asyncio.run(test_multiple_execution())
