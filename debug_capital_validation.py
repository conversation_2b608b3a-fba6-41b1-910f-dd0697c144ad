#!/usr/bin/env python3
"""
Debug do problema de validação de capital
Simula o cenário exato dos logs fornecidos
"""

import sys
import os
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

@dataclass
class MockTradingSignal:
    """Mock do TradingSignal para debug"""
    symbol: str
    direction: str
    confidence_score: float
    position_size_usd: float
    entry_price: float
    target_price: float
    stop_price: float
    quantum_metrics: Dict

@dataclass
class MockTradeResult:
    """Mock do TradeResult para debug"""
    signal: MockTradingSignal
    order_id: str
    executed_price: float
    executed_quantity: float
    pnl: float = 0.0
    outcome: str = 'pending'

class DebugCapitalValidation:
    """Debug da validação de capital"""
    
    def __init__(self):
        # Simular estado após 2 trades executados
        self.balance_usdt = 76.51  # Balance após 2 trades
        self.initial_balance = 96.51  # Balance inicial estimado
        
        # Configurações de risco
        self.risk_limits = {
            'max_exposure_pct': 0.42,  # 42% (aumentado)
        }
        
        # Trades ativos simulados
        self.active_trades = {}
        
        # Simular trades já executados
        ada_signal = MockTradingSignal(
            symbol='ADA/USDT',
            direction='buy',
            confidence_score=0.839,
            position_size_usd=10.0,
            entry_price=0.7499,
            target_price=0.7559,
            stop_price=0.7469,
            quantum_metrics={}
        )
        
        uni_signal = MockTradingSignal(
            symbol='UNI/USDT',
            direction='buy',
            confidence_score=0.772,
            position_size_usd=10.0,
            entry_price=8.5350,
            target_price=8.6033,
            stop_price=8.5009,
            quantum_metrics={}
        )
        
        # Adicionar aos trades ativos
        self.active_trades['7290656394'] = MockTradeResult(
            signal=ada_signal,
            order_id='7290656394',
            executed_price=0.7500,
            executed_quantity=13.3
        )
        
        self.active_trades['4092197171'] = MockTradeResult(
            signal=uni_signal,
            order_id='4092197171',
            executed_price=8.5400,
            executed_quantity=1.17
        )
        
        print(f"🔍 ESTADO INICIAL DO DEBUG:")
        print(f"   Balance atual: ${self.balance_usdt:.2f}")
        print(f"   Balance inicial estimado: ${self.initial_balance:.2f}")
        print(f"   Trades ativos: {len(self.active_trades)}")
        print(f"   Limite de exposição: 42%")
        print()

    def has_sufficient_capital(self, signal: MockTradingSignal) -> Tuple[bool, str]:
        """Versão debug da validação de capital"""
        try:
            # Calcular exposição atual
            current_exposure = sum(trade.signal.position_size_usd for trade in self.active_trades.values())
            
            # Calcular exposição total se executar este sinal
            total_exposure = current_exposure + signal.position_size_usd
            
            # Verificar se não excede limite de exposição
            max_exposure = self.balance_usdt * self.risk_limits['max_exposure_pct']
            
            # Debug detalhado
            print(f"💰 VALIDAÇÃO DE CAPITAL para {signal.symbol}:")
            print(f"   Balance atual: ${self.balance_usdt:.2f}")
            print(f"   Exposição atual: ${current_exposure:.2f}")
            print(f"   Novo trade: ${signal.position_size_usd:.2f}")
            print(f"   Exposição total: ${total_exposure:.2f}")
            print(f"   Limite máximo: ${max_exposure:.2f} (42% de ${self.initial_balance:.2f})")
            print(f"   Trades ativos: {len(self.active_trades)}")
            print()

            # Calcular com balance inicial (correto)
            max_exposure_correto = self.initial_balance * self.risk_limits['max_exposure_pct']
            print(f"✅ CÁLCULO COM NOVO LIMITE (42%):")
            print(f"   Limite: ${max_exposure_correto:.2f} (42% de ${self.initial_balance:.2f})")
            print(f"   Exposição total: ${total_exposure:.2f}")
            print(f"   Dentro do limite? {total_exposure <= max_exposure_correto}")
            print()
            
            if total_exposure > max_exposure:
                reason = f"Exposição máxima atingida: ${total_exposure:.2f} > ${max_exposure:.2f} (42%)"
                return False, reason
            
            # Verificar se há saldo suficiente
            if signal.position_size_usd > self.balance_usdt:
                reason = f"Capital insuficiente: ${signal.position_size_usd:.2f} > ${self.balance_usdt:.2f}"
                return False, reason
                
            return True, "OK"
            
        except Exception as e:
            return False, f"Erro: {e}"

def test_capital_validation():
    """Testa a validação de capital com cenário real"""
    print("🧪 DEBUG: VALIDAÇÃO DE CAPITAL")
    print("=" * 60)
    
    debug = DebugCapitalValidation()
    
    # Criar terceiro sinal (SAND/USDT) que foi rejeitado
    sand_signal = MockTradingSignal(
        symbol='SAND/USDT',
        direction='buy',
        confidence_score=0.700,  # Estimado
        position_size_usd=10.0,
        entry_price=0.5000,  # Estimado
        target_price=0.5040,  # +0.8%
        stop_price=0.4980,    # -0.4%
        quantum_metrics={}
    )
    
    print(f"🎯 TESTANDO TERCEIRO SINAL: {sand_signal.symbol}")
    print()
    
    # Testar validação
    capital_ok, reason = debug.has_sufficient_capital(sand_signal)
    
    print(f"📊 RESULTADO:")
    print(f"   Validação passou? {capital_ok}")
    print(f"   Motivo: {reason}")
    print()
    
    # Análise do resultado
    print("🔍 ANÁLISE COM NOVO LIMITE (42%):")
    print("   Balance inicial: ~$96.51")
    print("   42% do inicial: $40.53")
    print("   Exposição atual: $20.00")
    print("   Novo trade: $10.00")
    print("   Total: $30.00 < $40.53 ✅")
    print()
    print("   CONCLUSÃO: Com 42% de limite, o terceiro trade deve ser aceito!")
    print("   Isso permite execução completa dos 3 sinais encontrados.")

if __name__ == "__main__":
    test_capital_validation()
