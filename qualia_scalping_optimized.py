#!/usr/bin/env python3
"""
QUALIA - Sistema de Scalping OTIMIZADO
Estratégia com Take Profit pequeno e Stop Loss realista

FILOSOFIA CORRIGIDA:
- Take Profit: PEQUENO E RÁPIDO (0.3-0.8%) - Capitaliza movimentos mínimos
- Stop Loss: AMPLO E PROTETOR (1.5-3%) - Absorve volatilidade natural
- Sistema sabe comprar - Dar margem para o mercado se mover

LÓGICA:
✅ TP: Ganhar pouco mas frequente
✅ SL: Proteger contra movimentos realmente adversos
✅ Diferentes propósitos = Diferentes parâmetros
"""

import asyncio
import ccxt
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import os
from dotenv import load_dotenv
import json

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_scalping_opt_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedScalpPosition:
    """Posição de scalping com parâmetros otimizados"""

    def __init__(self, symbol: str, quantity: float, entry_price: float,
                 entry_time: datetime, confidence: float):
        self.symbol = symbol
        self.quantity = quantity
        self.entry_price = entry_price
        self.entry_time = entry_time
        self.confidence = confidence

        # PARÂMETROS OTIMIZADOS (LÓGICAS DIFERENTES)

        # Take Profit: PEQUENO E RÁPIDO (0.3-0.8%)
        # Objetivo: Capitalizar movimentos mínimos acima dos fees
        self.take_profit_pct = 0.003 + (confidence - 0.4) * 0.005  # 0.3-0.8%

        # Stop Loss: AMPLO E PROTETOR (1.5-3%)
        # Objetivo: Absorver volatilidade natural do crypto
        # Sistema sabe comprar - dar margem para mercado se mover
        self.stop_loss_pct = 0.015 + (1 - confidence) * 0.015      # 1.5-3%

        # Tempo máximo: Baseado na confiança (5-15 minutos)
        self.max_duration_minutes = 5 + int(confidence * 10)

        # Preços de saída
        self.take_profit_price = entry_price * (1 + self.take_profit_pct)
        self.stop_loss_price = entry_price * (1 - self.stop_loss_pct)

        # Tempo limite
        self.max_time = entry_time + timedelta(minutes=self.max_duration_minutes)

        # Status
        self.is_open = True
        self.exit_price = None
        self.exit_time = None
        self.exit_reason = None
        self.pnl = 0.0

        logger.info(f"🎯 SCALP OTIMIZADO: {symbol}")
        logger.info(f"   Entrada: ${entry_price:.2f}")
        logger.info(f"   TP: ${self.take_profit_price:.2f} (+{self.take_profit_pct:.2%}) [RÁPIDO]")
        logger.info(f"   SL: ${self.stop_loss_price:.2f} (-{self.stop_loss_pct:.2%}) [AMPLO]")
        logger.info(f"   Tempo: {self.max_duration_minutes}min")
        logger.info(f"   Ratio TP/SL: {self.take_profit_pct/self.stop_loss_pct:.2f}")

    def check_exit_conditions(self, current_price: float, current_time: datetime) -> Optional[str]:
        """Verifica condições de saída otimizadas"""

        if not self.is_open:
            return None

        # 1. Take profit (PRIORIDADE - PEQUENO MAS RÁPIDO)
        if current_price >= self.take_profit_price:
            return 'take_profit'

        # 2. Stop loss (PROTEÇÃO - AMPLO PARA VOLATILIDADE)
        if current_price <= self.stop_loss_price:
            return 'stop_loss'

        # 3. Tempo limite (FORÇAR SAÍDA)
        if current_time >= self.max_time:
            return 'time_limit'

        # 4. Micro-profit inteligente (após 60% do tempo)
        time_elapsed = current_time - self.entry_time
        time_pct = time_elapsed.total_seconds() / (self.max_duration_minutes * 60)

        if time_pct > 0.6:  # Após 60% do tempo
            profit_pct = (current_price - self.entry_price) / self.entry_price
            # Se tem pelo menos 0.25% (acima dos fees)
            if profit_pct >= 0.0025:
                return 'micro_profit'

        # 5. Trailing stop dinâmico (se passou 50% do TP)
        profit_threshold = self.entry_price * (1 + self.take_profit_pct * 0.5)
        if current_price >= profit_threshold:
            # Trailing stop a 0.5% do pico
            trailing_stop = current_price * 0.995
            if trailing_stop > self.stop_loss_price:
                self.stop_loss_price = trailing_stop
                logger.info(f"   Trailing SL ajustado: ${trailing_stop:.2f}")

        return None

    def close_position(self, exit_price: float, exit_reason: str):
        """Fecha posição otimizada"""

        self.is_open = False
        self.exit_price = exit_price
        self.exit_time = datetime.now()
        self.exit_reason = exit_reason

        # P&L bruto
        gross_pnl = (exit_price - self.entry_price) * self.quantity

        # Fees (0.1% entrada + 0.1% saída)
        entry_fee = self.entry_price * self.quantity * 0.001
        exit_fee = exit_price * self.quantity * 0.001
        total_fees = entry_fee + exit_fee

        # P&L líquido
        self.pnl = gross_pnl - total_fees

        # Percentuais
        gross_pct = (exit_price - self.entry_price) / self.entry_price
        net_pct = self.pnl / (self.entry_price * self.quantity)

        duration = self.exit_time - self.entry_time

        logger.info(f"💰 SCALP FECHADO: {self.symbol}")
        logger.info(f"   Razão: {exit_reason}")
        logger.info(f"   Duração: {duration}")
        logger.info(f"   Saída: ${exit_price:.2f}")
        logger.info(f"   P&L Bruto: ${gross_pnl:.4f} ({gross_pct:+.3%})")
        logger.info(f"   Fees: ${total_fees:.4f}")
        logger.info(f"   P&L Líquido: ${self.pnl:.4f} ({net_pct:+.3%})")

class QualiaScalpingOptimized:
    """Sistema de scalping com parâmetros otimizados"""

    def __init__(self, phase: int = 1):
        # Configurações otimizadas
        self.phases = {
            1: {'capital': 50, 'max_loss': 5, 'position_size': 10, 'max_positions': 2},
            2: {'capital': 200, 'max_loss': 15, 'position_size': 30, 'max_positions': 3},
            3: {'capital': 500, 'max_loss': 35, 'position_size': 70, 'max_positions': 4}
        }

        self.current_phase = phase
        self.phase_config = self.phases[phase]

        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')

        # Estado
        self.exchange = None
        self.running = False

        # Gestão de posições
        self.open_positions: Dict[str, OptimizedScalpPosition] = {}
        self.closed_positions: List[OptimizedScalpPosition] = []
        self.total_pnl = 0.0
        self.scalps_today = 0

        # Ativos para scalping (MÁXIMA LIQUIDEZ)
        self.symbols = ['BTC/USDT', 'ETH/USDT']

        # Parâmetros quânticos validados
        self.quantum_params = {
            'consciousness_threshold': 0.4,
            'coherence_threshold': 0.35,
            'confidence_threshold': 0.4
        }

        logger.info(f"🎯 QUALIA Scalping OTIMIZADO - Fase {phase}")
        logger.info(f"💰 Capital: ${self.phase_config['capital']}")
        logger.info(f"📊 Posição: ${self.phase_config['position_size']} por trade")
        logger.info(f"⚡ TP: 0.3-0.8% (rápido) | SL: 1.5-3% (proteção)")

    async def initialize(self):
        """Inicializa sistema otimizado"""
        try:
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,
                'enableRateLimit': True,
            })

            markets = self.exchange.load_markets()
            balance = self.exchange.fetch_balance()

            usdt_free = balance.get('USDT', {}).get('free', 0)

            logger.info(f"✅ Conectado - USDT: ${usdt_free:.2f}")

            if usdt_free < self.phase_config['capital']:
                logger.error(f"❌ Capital insuficiente: ${usdt_free:.2f}")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ Erro: {e}")
            return False

    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de mercado"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)

            return {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'spread': ticker['ask'] - ticker['bid'],
                'volume': ticker['baseVolume'],
                'change_24h': ticker['percentage'] / 100 if ticker['percentage'] else 0,
                'timestamp': datetime.now()
            }

        except Exception as e:
            logger.error(f"❌ Erro obtendo dados {symbol}: {e}")
            return None

    def calculate_optimized_signal(self, market_data: Dict) -> Dict:
        """Calcula sinal otimizado para scalping"""

        volatility = abs(market_data['change_24h'])
        spread_pct = market_data['spread'] / market_data['price']
        volume_strength = min(market_data['volume'] / 1000, 1.0)

        # Fórmulas otimizadas (mais permissivas para scalping)
        coherence = max(0.0, min(1.0, 0.85 - volatility * 3 - spread_pct * 50))
        consciousness = max(0.0, min(1.0, 0.8 + volume_strength * 0.4 - volatility * 2))
        retrocausal_protection = max(0.0, min(1.0, 0.75 - spread_pct * 30))

        confidence = (coherence + consciousness + retrocausal_protection) / 3

        # Verificar thresholds
        thresholds_met = {
            'consciousness': consciousness >= self.quantum_params['consciousness_threshold'],
            'coherence': coherence >= self.quantum_params['coherence_threshold'],
            'confidence': confidence >= self.quantum_params['confidence_threshold']
        }

        # Condições específicas para scalping
        spread_ok = spread_pct < 0.001  # Spread baixo
        volume_ok = volume_strength > 0.3  # Volume adequado
        volatility_ok = volatility < 0.05  # Não muito volátil

        # Gerar sinal
        if all(thresholds_met.values()) and spread_ok and volume_ok and volatility_ok:
            if market_data['change_24h'] > -0.02:
                action = 'buy' if consciousness > coherence else 'hold'
            else:
                action = 'hold'
        else:
            action = 'hold'

        return {
            'action': action,
            'confidence': confidence,
            'coherence': coherence,
            'consciousness': consciousness,
            'retrocausal_protection': retrocausal_protection,
            'spread_quality': 1 - spread_pct * 1000,
            'volume_strength': volume_strength,
            'volatility': volatility,
            'market_data': market_data,
            'thresholds_met': thresholds_met,
            'scalping_conditions': {
                'spread_ok': spread_ok,
                'volume_ok': volume_ok,
                'volatility_ok': volatility_ok
            }
        }

    async def open_optimized_position(self, signal: Dict) -> Optional[OptimizedScalpPosition]:
        """Abre posição otimizada"""

        symbol = signal['market_data']['symbol']

        # Verificações
        if symbol in self.open_positions:
            return None

        if len(self.open_positions) >= self.phase_config['max_positions']:
            return None

        if self.total_pnl <= -self.phase_config['max_loss']:
            logger.warning("🚫 Limite de perda atingido")
            return None

        try:
            confidence = signal['confidence']
            price = signal['market_data']['price']

            # Tamanho da posição
            position_size = self.phase_config['position_size']
            quantity = position_size / price

            logger.info(f"🎯 ABRINDO SCALP OTIMIZADO: {symbol}")
            logger.info(f"   Tamanho: ${position_size:.2f}")
            logger.info(f"   Confiança: {confidence:.3f}")
            logger.info(f"   Volatilidade: {signal['volatility']:.3f}")

            # Executar ordem
            order = self.exchange.create_market_buy_order(symbol, quantity)

            if order:
                # Processar ordem
                filled_qty = order.get('filled', 0) or quantity
                avg_price = order.get('average') or price

                # Criar posição otimizada
                position = OptimizedScalpPosition(
                    symbol=symbol,
                    quantity=filled_qty,
                    entry_price=avg_price,
                    entry_time=datetime.now(),
                    confidence=confidence
                )

                # Adicionar às posições
                self.open_positions[symbol] = position
                self.scalps_today += 1

                logger.info(f"✅ SCALP OTIMIZADO ABERTO: {symbol}")

                return position
            else:
                logger.error("❌ Falha na ordem")
                return None

        except Exception as e:
            logger.error(f"❌ Erro abrindo posição: {e}")
            return None

    async def close_optimized_position(self, position: OptimizedScalpPosition, exit_reason: str) -> bool:
        """Fecha posição otimizada"""

        try:
            symbol = position.symbol
            quantity = position.quantity

            logger.info(f"🎯 FECHANDO SCALP: {symbol} ({exit_reason})")

            # Executar ordem de venda
            order = self.exchange.create_market_sell_order(symbol, quantity)

            if order:
                # Processar ordem
                avg_price = order.get('average') or order.get('price', 0)

                # Fechar posição
                position.close_position(avg_price, exit_reason)

                # Atualizar totais
                self.total_pnl += position.pnl

                # Mover para fechadas
                self.closed_positions.append(position)
                del self.open_positions[symbol]

                logger.info(f"✅ SCALP FECHADO: {symbol}")
                logger.info(f"   P&L Total: ${self.total_pnl:.4f}")

                return True
            else:
                logger.error("❌ Falha na venda")
                return False

        except Exception as e:
            logger.error(f"❌ Erro fechando posição: {e}")
            return False