#!/bin/bash

echo "========================================"
echo "   QUALIA KILL ALL - CANCELADOR"
echo "========================================"
echo ""
echo "ATENÇÃO: Este script cancela TODAS as execuções QUALIA ativas!"
echo ""
echo "- Supervisor QUALIA"
echo "- Sistemas de trading"
echo "- Processos de monitoramento"
echo "- Scripts adaptativos"
echo ""
echo "========================================"
echo ""

# Verificar se Python esta instalado
if ! command -v python3 &> /dev/null; then
    echo "ERRO: Python3 não encontrado!"
    echo "Instale Python 3.8+ e tente novamente."
    exit 1
fi

# Verificar se o arquivo killer existe
if [ ! -f "qualia_kill_all.py" ]; then
    echo "ERRO: qualia_kill_all.py não encontrado!"
    echo "Certifique-se de estar no diretório correto."
    exit 1
fi

echo "Iniciando cancelamento de execuções..."
echo ""

# Tornar o script executável
chmod +x qualia_kill_all.py

# Executar cancelador
python3 qualia_kill_all.py

echo ""
echo "========================================"
echo "Cancelamento concluído!"
echo "Verifique os logs para detalhes."
echo "========================================"
