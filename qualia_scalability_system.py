#!/usr/bin/env python3
"""
QUALIA Scalability System - Crescimento Exponencial
Sistema de escalabilidade automática baseado nas recomendações finais

ESTRATÉGIA DE ESCALABILIDADE:
- Semana 1: $200 → $600 (confirmar consistência)
- Mês 1: $600 → $5,000 (escalar performance)
- Mês 2+: $5,000+ (crescimento exponencial)

CARACTERÍSTICAS:
- Ajuste automático de parâmetros conforme capital
- Reinvestimento inteligente de lucros
- Gestão de risco escalável
- Monitoramento de performance por fase
- Otimização contínua
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional
from enum import Enum
import numpy as np

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_scalability_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScalabilityPhase(Enum):
    """Fases de escalabilidade"""
    VALIDATION = "validation"      # $200-600 (Semana 1)
    SCALING = "scaling"           # $600-5000 (Mês 1)
    EXPONENTIAL = "exponential"   # $5000+ (Mês 2+)

@dataclass
class ScalabilityConfig:
    """Configuração por fase de escalabilidade"""
    phase: ScalabilityPhase
    min_capital: float
    max_capital: float
    position_size_pct: float
    profit_target_pct: float
    stop_loss_pct: float
    max_daily_trades: int
    reinvestment_rate: float
    risk_tolerance: float
    ada_priority_multiplier: float
    
    def get_phase_name(self) -> str:
        return self.phase.value.upper()

class QualiaScalabilitySystem:
    """Sistema de Escalabilidade QUALIA"""
    
    def __init__(self):
        self.current_capital = 200.0
        self.initial_capital = 200.0
        self.current_phase = ScalabilityPhase.VALIDATION
        self.phase_start_time = datetime.now()
        self.phase_start_capital = 200.0
        
        # Histórico de performance
        self.phase_history = []
        self.daily_performance = []
        self.scalability_metrics = {}
        
        # Configurações por fase
        self.phase_configs = {
            ScalabilityPhase.VALIDATION: ScalabilityConfig(
                phase=ScalabilityPhase.VALIDATION,
                min_capital=200.0,
                max_capital=600.0,
                position_size_pct=0.08,      # 8% (agressivo para crescimento rápido)
                profit_target_pct=0.008,     # 0.8% (validado)
                stop_loss_pct=0.004,         # 0.4% (proteção)
                max_daily_trades=15,         # Moderado
                reinvestment_rate=0.8,       # 80% reinvestimento
                risk_tolerance=0.05,         # 5% drawdown máximo
                ada_priority_multiplier=1.3  # Prioridade alta para ADA
            ),
            
            ScalabilityPhase.SCALING: ScalabilityConfig(
                phase=ScalabilityPhase.SCALING,
                min_capital=600.0,
                max_capital=5000.0,
                position_size_pct=0.06,      # 6% (mais conservador com capital maior)
                profit_target_pct=0.007,     # 0.7% (ligeiramente menor)
                stop_loss_pct=0.0035,        # 0.35% (proteção maior)
                max_daily_trades=20,         # Mais trades
                reinvestment_rate=0.7,       # 70% reinvestimento
                risk_tolerance=0.04,         # 4% drawdown máximo
                ada_priority_multiplier=1.5  # Prioridade muito alta para ADA
            ),
            
            ScalabilityPhase.EXPONENTIAL: ScalabilityConfig(
                phase=ScalabilityPhase.EXPONENTIAL,
                min_capital=5000.0,
                max_capital=float('inf'),
                position_size_pct=0.04,      # 4% (conservador com capital alto)
                profit_target_pct=0.006,     # 0.6% (mais conservador)
                stop_loss_pct=0.003,         # 0.3% (proteção máxima)
                max_daily_trades=25,         # Máximo trades
                reinvestment_rate=0.6,       # 60% reinvestimento
                risk_tolerance=0.03,         # 3% drawdown máximo
                ada_priority_multiplier=2.0  # Prioridade máxima para ADA
            )
        }
    
    def get_current_config(self) -> ScalabilityConfig:
        """Obtém configuração da fase atual"""
        return self.phase_configs[self.current_phase]
    
    def should_advance_phase(self) -> bool:
        """Verifica se deve avançar para próxima fase"""
        current_config = self.get_current_config()
        
        # Critérios para avanço de fase
        capital_threshold_met = self.current_capital >= current_config.max_capital * 0.9
        time_threshold_met = datetime.now() - self.phase_start_time >= timedelta(days=7)  # Mínimo 1 semana
        
        return capital_threshold_met and time_threshold_met
    
    def advance_to_next_phase(self):
        """Avança para próxima fase de escalabilidade"""
        old_phase = self.current_phase
        
        # Registrar performance da fase atual
        phase_duration = datetime.now() - self.phase_start_time
        phase_return = (self.current_capital - self.phase_start_capital) / self.phase_start_capital
        
        phase_record = {
            'phase': old_phase.value,
            'start_time': self.phase_start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'duration_days': phase_duration.days,
            'start_capital': self.phase_start_capital,
            'end_capital': self.current_capital,
            'return_pct': phase_return * 100,
            'success': phase_return > 0
        }
        
        self.phase_history.append(phase_record)
        
        # Avançar fase
        if self.current_phase == ScalabilityPhase.VALIDATION:
            self.current_phase = ScalabilityPhase.SCALING
        elif self.current_phase == ScalabilityPhase.SCALING:
            self.current_phase = ScalabilityPhase.EXPONENTIAL
        
        # Resetar métricas da nova fase
        self.phase_start_time = datetime.now()
        self.phase_start_capital = self.current_capital
        
        logger.info("=" * 60)
        logger.info("AVANCO DE FASE DE ESCALABILIDADE")
        logger.info("=" * 60)
        logger.info(f"Fase anterior: {old_phase.value.upper()}")
        logger.info(f"Nova fase: {self.current_phase.value.upper()}")
        logger.info(f"Capital: ${self.current_capital:.2f}")
        logger.info(f"Retorno da fase: {phase_return:.2%}")
        logger.info(f"Duracao: {phase_duration.days} dias")
        logger.info("=" * 60)
    
    def calculate_optimal_position_size(self, base_signal_confidence: float) -> float:
        """Calcula position size otimizado para fase atual"""
        config = self.get_current_config()
        
        # Position size base da fase
        base_size = config.position_size_pct
        
        # Ajuste por confiança do sinal
        confidence_multiplier = 0.5 + (base_signal_confidence * 0.5)  # 0.5 a 1.0
        
        # Ajuste por performance recente
        if len(self.daily_performance) >= 3:
            recent_performance = np.mean([p['return_pct'] for p in self.daily_performance[-3:]])
            performance_multiplier = 1.0 + (recent_performance / 100) * 0.2  # Máximo ±20%
            performance_multiplier = max(0.8, min(1.2, performance_multiplier))
        else:
            performance_multiplier = 1.0
        
        # Position size final
        optimal_size = base_size * confidence_multiplier * performance_multiplier
        
        # Limites por fase
        if self.current_phase == ScalabilityPhase.VALIDATION:
            optimal_size = max(0.06, min(0.10, optimal_size))
        elif self.current_phase == ScalabilityPhase.SCALING:
            optimal_size = max(0.04, min(0.08, optimal_size))
        else:  # EXPONENTIAL
            optimal_size = max(0.02, min(0.06, optimal_size))
        
        return optimal_size
    
    def calculate_reinvestment_amount(self, daily_profit: float) -> float:
        """Calcula valor a ser reinvestido"""
        config = self.get_current_config()
        
        if daily_profit <= 0:
            return 0.0
        
        # Valor base de reinvestimento
        base_reinvestment = daily_profit * config.reinvestment_rate
        
        # Ajuste por fase (mais agressivo nas fases iniciais)
        if self.current_phase == ScalabilityPhase.VALIDATION:
            phase_multiplier = 1.2  # 20% mais agressivo
        elif self.current_phase == ScalabilityPhase.SCALING:
            phase_multiplier = 1.1  # 10% mais agressivo
        else:
            phase_multiplier = 1.0  # Padrão
        
        reinvestment = base_reinvestment * phase_multiplier
        
        # Limite máximo (não reinvestir mais que 90% do lucro)
        max_reinvestment = daily_profit * 0.9
        
        return min(reinvestment, max_reinvestment)
    
    def get_ada_priority_multiplier(self) -> float:
        """Obtém multiplicador de prioridade para ADA-USDT"""
        config = self.get_current_config()
        
        # Multiplicador base da fase
        base_multiplier = config.ada_priority_multiplier
        
        # Ajuste por performance do ADA (se disponível)
        # Em produção real, seria baseado em métricas históricas
        ada_performance_bonus = 0.1  # Baseado na validação (77.8% dos trades)
        
        return base_multiplier + ada_performance_bonus
    
    def update_daily_performance(self, daily_return_pct: float, trades_count: int, ada_trades_count: int):
        """Atualiza performance diária"""
        daily_record = {
            'date': datetime.now().date().isoformat(),
            'phase': self.current_phase.value,
            'capital_start': self.current_capital / (1 + daily_return_pct / 100),
            'capital_end': self.current_capital,
            'return_pct': daily_return_pct,
            'trades_count': trades_count,
            'ada_trades_count': ada_trades_count,
            'ada_percentage': (ada_trades_count / trades_count) if trades_count > 0 else 0
        }
        
        self.daily_performance.append(daily_record)
        
        # Manter apenas últimos 30 dias
        if len(self.daily_performance) > 30:
            self.daily_performance = self.daily_performance[-30:]
    
    def calculate_scalability_metrics(self) -> Dict:
        """Calcula métricas de escalabilidade"""
        if not self.daily_performance:
            return {}
        
        # Métricas gerais
        total_days = len(self.daily_performance)
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        daily_returns = [p['return_pct'] for p in self.daily_performance]
        
        avg_daily_return = np.mean(daily_returns)
        volatility = np.std(daily_returns)
        sharpe_ratio = avg_daily_return / volatility if volatility > 0 else 0
        
        # Métricas por fase
        phase_metrics = {}
        for phase_record in self.phase_history:
            phase_name = phase_record['phase']
            phase_metrics[phase_name] = {
                'duration_days': phase_record['duration_days'],
                'return_pct': phase_record['return_pct'],
                'capital_growth': phase_record['end_capital'] / phase_record['start_capital'],
                'success': phase_record['success']
            }
        
        # Projeções
        if avg_daily_return > 0:
            days_to_double = 100 / avg_daily_return  # Dias para dobrar capital
            monthly_projection = avg_daily_return * 30
            yearly_projection = avg_daily_return * 365
        else:
            days_to_double = float('inf')
            monthly_projection = 0
            yearly_projection = 0
        
        return {
            'current_phase': self.current_phase.value,
            'total_days': total_days,
            'total_return_pct': total_return * 100,
            'avg_daily_return_pct': avg_daily_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'days_to_double': days_to_double,
            'monthly_projection_pct': monthly_projection,
            'yearly_projection_pct': yearly_projection,
            'phase_metrics': phase_metrics,
            'capital_growth_factor': self.current_capital / self.initial_capital
        }
    
    def get_current_trading_parameters(self) -> Dict:
        """Obtém parâmetros de trading para fase atual"""
        config = self.get_current_config()
        
        return {
            'phase': config.get_phase_name(),
            'position_size_base': config.position_size_pct,
            'profit_target': config.profit_target_pct,
            'stop_loss': config.stop_loss_pct,
            'max_daily_trades': config.max_daily_trades,
            'reinvestment_rate': config.reinvestment_rate,
            'risk_tolerance': config.risk_tolerance,
            'ada_priority_multiplier': config.ada_priority_multiplier,
            'capital_range': f"${config.min_capital:.0f} - ${config.max_capital:.0f}" if config.max_capital != float('inf') else f"${config.min_capital:.0f}+"
        }
    
    def generate_scalability_report(self) -> Dict:
        """Gera relatório completo de escalabilidade"""
        current_config = self.get_current_config()
        scalability_metrics = self.calculate_scalability_metrics()
        trading_params = self.get_current_trading_parameters()
        
        # Progresso da fase atual
        phase_duration = datetime.now() - self.phase_start_time
        phase_progress = (self.current_capital - self.phase_start_capital) / self.phase_start_capital
        
        # Próximos marcos
        if self.current_phase == ScalabilityPhase.VALIDATION:
            next_target = 600.0
            next_phase = "SCALING"
        elif self.current_phase == ScalabilityPhase.SCALING:
            next_target = 5000.0
            next_phase = "EXPONENTIAL"
        else:
            next_target = self.current_capital * 2  # Dobrar capital
            next_phase = "EXPONENTIAL (CONTINUED)"
        
        progress_to_next = (self.current_capital / next_target) * 100
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system': 'QUALIA Scalability System',
            
            'current_status': {
                'capital': self.current_capital,
                'phase': current_config.get_phase_name(),
                'phase_duration_days': phase_duration.days,
                'phase_progress_pct': phase_progress * 100,
                'next_target': next_target,
                'next_phase': next_phase,
                'progress_to_next_pct': min(progress_to_next, 100)
            },
            
            'scalability_metrics': scalability_metrics,
            'trading_parameters': trading_params,
            'phase_history': self.phase_history,
            
            'recommendations': {
                'should_advance_phase': self.should_advance_phase(),
                'optimal_position_size_range': f"{current_config.position_size_pct*0.8:.1%} - {current_config.position_size_pct*1.2:.1%}",
                'ada_priority': f"{current_config.ada_priority_multiplier:.1f}x",
                'reinvestment_strategy': f"{current_config.reinvestment_rate:.0%} dos lucros"
            }
        }

def main():
    """Demonstração do sistema de escalabilidade"""
    
    print("=" * 70)
    print("QUALIA SCALABILITY SYSTEM - CRESCIMENTO EXPONENCIAL")
    print("=" * 70)
    print("ESTRATEGIA DE ESCALABILIDADE:")
    print("- Semana 1: $200 → $600 (VALIDATION)")
    print("- Mes 1: $600 → $5,000 (SCALING)")
    print("- Mes 2+: $5,000+ (EXPONENTIAL)")
    print("=" * 70)
    
    # Inicializar sistema
    scalability = QualiaScalabilitySystem()
    
    # Simular evolução do capital
    print(f"\nFase atual: {scalability.current_phase.value.upper()}")
    print(f"Capital atual: ${scalability.current_capital:.2f}")
    
    # Mostrar parâmetros atuais
    params = scalability.get_current_trading_parameters()
    print(f"\nParametros de trading:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # Simular alguns dias de trading
    print(f"\nSimulando crescimento...")
    
    for day in range(1, 8):  # 7 dias
        # Simular retorno diário baseado na validação
        daily_return = np.random.normal(0.72, 0.3)  # Baseado na validação (+0.72% em 4min)
        daily_return = max(-2, min(5, daily_return))  # Limitar entre -2% e +5%
        
        old_capital = scalability.current_capital
        scalability.current_capital *= (1 + daily_return / 100)
        
        # Simular trades
        trades_count = np.random.randint(3, 8)
        ada_trades = int(trades_count * 0.78)  # 78% ADA baseado na validação
        
        scalability.update_daily_performance(daily_return, trades_count, ada_trades)
        
        print(f"Dia {day}: ${old_capital:.2f} → ${scalability.current_capital:.2f} ({daily_return:+.2f}%)")
        
        # Verificar avanço de fase
        if scalability.should_advance_phase():
            scalability.advance_to_next_phase()
    
    # Gerar relatório final
    report = scalability.generate_scalability_report()
    
    print(f"\n" + "=" * 60)
    print("RELATORIO DE ESCALABILIDADE")
    print("=" * 60)
    
    status = report['current_status']
    metrics = report['scalability_metrics']
    recommendations = report['recommendations']
    
    print(f"\nSTATUS ATUAL:")
    print(f"  Capital: ${status['capital']:.2f}")
    print(f"  Fase: {status['phase']}")
    print(f"  Progresso da fase: {status['phase_progress_pct']:+.2f}%")
    print(f"  Proximo target: ${status['next_target']:.2f}")
    print(f"  Progresso para proximo: {status['progress_to_next_pct']:.1f}%")
    
    if metrics:
        print(f"\nMETRICAS:")
        print(f"  Retorno total: {metrics['total_return_pct']:+.2f}%")
        print(f"  Retorno diario medio: {metrics['avg_daily_return_pct']:+.2f}%")
        print(f"  Sharpe ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"  Dias para dobrar: {metrics['days_to_double']:.1f}")
        print(f"  Projecao mensal: {metrics['monthly_projection_pct']:+.2f}%")
    
    print(f"\nRECOMENDACAES:")
    print(f"  Avancar fase: {'SIM' if recommendations['should_advance_phase'] else 'NAO'}")
    print(f"  Position size: {recommendations['optimal_position_size_range']}")
    print(f"  Prioridade ADA: {recommendations['ada_priority']}")
    print(f"  Reinvestimento: {recommendations['reinvestment_strategy']}")
    
    # Salvar relatório
    filename = f"qualia_scalability_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\nRelatorio salvo: {filename}")
    print(f"\n🚀 Sistema de escalabilidade QUALIA operacional!")

if __name__ == "__main__":
    main()
