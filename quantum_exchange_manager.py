#!/usr/bin/env python3
"""
QUALIA - Gerenciador de Exchange Quântico
Sistema robusto para conexão com exchanges reais e modo simulação
"""

import asyncio
import json
import logging
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import ccxt.async_support as ccxt_async
from pathlib import Path

logger = logging.getLogger(__name__)

class QuantumExchangeManager:
    """Gerenciador robusto de conexões com exchanges"""
    
    def __init__(self, config_file: str = "exchange_config_template.json"):
        self.config_file = config_file
        self.config = {}
        self.exchange = None
        self.is_connected = False
        self.simulation_mode = True
        
        # Dados simulados para fallback
        self.simulated_prices = {
            'BTC/USDT': 50000,
            'ETH/USDT': 3000,
            'XMR/USDT': 200,
            'ADA/USDT': 0.5,
            'DOT/USDT': 8.0
        }
        
    async def initialize(self) -> bool:
        """Inicializa o gerenciador de exchange"""
        try:
            # Carregar configuração
            await self.load_config()
            
            # Tentar conectar com exchange real
            success = await self.connect_to_exchange()
            
            if success:
                logger.info("✅ Exchange Manager inicializado com conexão real")
                self.simulation_mode = False
            else:
                logger.info("📊 Exchange Manager inicializado em modo simulação")
                self.simulation_mode = True
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro inicializando Exchange Manager: {e}")
            self.simulation_mode = True
            return False
    
    async def load_config(self):
        """Carrega configuração de arquivo"""
        try:
            config_path = Path(self.config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"✅ Configuração carregada de {self.config_file}")
            else:
                logger.warning(f"⚠️ Arquivo de configuração {self.config_file} não encontrado")
                # Usar configuração padrão
                self.config = {
                    "active_config": "simulation",
                    "exchange_configs": {
                        "simulation": {"name": "simulation"}
                    }
                }
        except Exception as e:
            logger.error(f"❌ Erro carregando configuração: {e}")
            self.config = {"active_config": "simulation"}
    
    async def connect_to_exchange(self) -> bool:
        """Conecta com exchange real"""
        try:
            active_config_name = self.config.get('active_config', 'simulation')
            
            if active_config_name == 'simulation':
                logger.info("📊 Configuração definida para simulação")
                return False
            
            exchange_configs = self.config.get('exchange_configs', {})
            exchange_config = exchange_configs.get(active_config_name)
            
            if not exchange_config:
                logger.warning(f"⚠️ Configuração '{active_config_name}' não encontrada")
                return False
            
            # Criar instância da exchange
            exchange_name = exchange_config.get('name')
            
            if exchange_name == 'binance':
                self.exchange = ccxt_async.binance({
                    'apiKey': exchange_config.get('api_key', ''),
                    'secret': exchange_config.get('secret', ''),
                    'sandbox': exchange_config.get('sandbox', True),
                    'enableRateLimit': True,
                    'options': exchange_config.get('options', {})
                })
                
                # Configurar URLs se especificado
                if 'urls' in exchange_config:
                    self.exchange.urls.update(exchange_config['urls'])
                    
            elif exchange_name == 'kraken':
                self.exchange = ccxt_async.kraken({
                    'apiKey': exchange_config.get('api_key', ''),
                    'secret': exchange_config.get('secret', ''),
                    'sandbox': exchange_config.get('sandbox', True),
                    'enableRateLimit': True
                })
            else:
                logger.warning(f"⚠️ Exchange '{exchange_name}' não suportada")
                return False
            
            # Testar conexão
            await self.exchange.load_markets()
            
            # Verificar se tem credenciais válidas
            if exchange_config.get('api_key') and exchange_config.get('secret'):
                try:
                    balance = await self.exchange.fetch_balance()
                    logger.info(f"✅ Conectado à {exchange_name} com credenciais válidas")
                    self.is_connected = True
                    return True
                except Exception as e:
                    logger.warning(f"⚠️ Credenciais inválidas para {exchange_name}: {e}")
                    await self.exchange.close()
                    self.exchange = None
                    return False
            else:
                logger.info(f"📊 Conectado à {exchange_name} sem credenciais (somente dados)")
                self.is_connected = True
                return True
                
        except Exception as e:
            logger.error(f"❌ Erro conectando com exchange: {e}")
            if self.exchange:
                await self.exchange.close()
                self.exchange = None
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de mercado (real ou simulado)"""
        try:
            if self.exchange and self.is_connected:
                # Dados reais da exchange
                ticker = await self.exchange.fetch_ticker(symbol)
                return {
                    'symbol': symbol,
                    'price': ticker['close'],
                    'volume': ticker['baseVolume'] or 0,
                    'high': ticker['high'],
                    'low': ticker['low'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'timestamp': datetime.now(),
                    'source': 'real'
                }
            else:
                # Dados simulados
                return self.generate_simulated_data(symbol)
                
        except Exception as e:
            logger.warning(f"⚠️ Erro obtendo dados reais de {symbol}: {e}")
            # Fallback para simulação
            return self.generate_simulated_data(symbol)
    
    def generate_simulated_data(self, symbol: str) -> Dict:
        """Gera dados de mercado simulados"""
        base_price = self.simulated_prices.get(symbol, 100)
        
        # Simular movimento de preço realista
        price_change = np.random.normal(0, 0.02)  # 2% volatilidade
        current_price = base_price * (1 + price_change)
        
        # Atualizar preço base para próxima iteração
        self.simulated_prices[symbol] = current_price
        
        # Simular spread bid/ask
        spread = current_price * 0.001  # 0.1% spread
        bid = current_price - spread/2
        ask = current_price + spread/2
        
        return {
            'symbol': symbol,
            'price': current_price,
            'volume': np.random.uniform(100, 1000),
            'high': current_price * (1 + abs(np.random.normal(0, 0.01))),
            'low': current_price * (1 - abs(np.random.normal(0, 0.01))),
            'bid': bid,
            'ask': ask,
            'timestamp': datetime.now(),
            'source': 'simulated'
        }
    
    async def get_balance(self) -> Dict:
        """Obtém saldo da conta"""
        try:
            if self.exchange and self.is_connected:
                balance = await self.exchange.fetch_balance()
                return {
                    'total': balance.get('total', {}),
                    'free': balance.get('free', {}),
                    'used': balance.get('used', {}),
                    'source': 'real'
                }
            else:
                # Saldo simulado
                return {
                    'total': {'USDT': 10000.0},
                    'free': {'USDT': 10000.0},
                    'used': {'USDT': 0.0},
                    'source': 'simulated'
                }
        except Exception as e:
            logger.error(f"❌ Erro obtendo saldo: {e}")
            return {
                'total': {'USDT': 10000.0},
                'free': {'USDT': 10000.0},
                'used': {'USDT': 0.0},
                'source': 'error_fallback'
            }
    
    async def place_order(self, symbol: str, side: str, amount: float, price: float = None) -> Dict:
        """Coloca ordem (real ou simulada)"""
        try:
            if self.exchange and self.is_connected and not self.simulation_mode:
                # Ordem real (cuidado!)
                if price:
                    order = await self.exchange.create_limit_order(symbol, side, amount, price)
                else:
                    order = await self.exchange.create_market_order(symbol, side, amount)
                
                return {
                    'id': order['id'],
                    'symbol': symbol,
                    'side': side,
                    'amount': amount,
                    'price': price or order.get('price'),
                    'status': order['status'],
                    'timestamp': datetime.now(),
                    'source': 'real'
                }
            else:
                # Ordem simulada
                order_id = f"sim_{int(datetime.now().timestamp())}"
                market_data = await self.get_market_data(symbol)
                execution_price = price or market_data['price']
                
                return {
                    'id': order_id,
                    'symbol': symbol,
                    'side': side,
                    'amount': amount,
                    'price': execution_price,
                    'status': 'filled',
                    'timestamp': datetime.now(),
                    'source': 'simulated'
                }
                
        except Exception as e:
            logger.error(f"❌ Erro colocando ordem: {e}")
            raise
    
    async def get_order_status(self, order_id: str, symbol: str) -> Dict:
        """Verifica status de ordem"""
        try:
            if self.exchange and self.is_connected and not self.simulation_mode:
                order = await self.exchange.fetch_order(order_id, symbol)
                return {
                    'id': order['id'],
                    'status': order['status'],
                    'filled': order['filled'],
                    'remaining': order['remaining'],
                    'source': 'real'
                }
            else:
                # Simular ordem preenchida
                return {
                    'id': order_id,
                    'status': 'filled',
                    'filled': 1.0,
                    'remaining': 0.0,
                    'source': 'simulated'
                }
        except Exception as e:
            logger.error(f"❌ Erro verificando ordem {order_id}: {e}")
            return {
                'id': order_id,
                'status': 'error',
                'source': 'error'
            }
    
    async def close(self):
        """Fecha conexões"""
        try:
            if self.exchange:
                await self.exchange.close()
                logger.info("✅ Conexão com exchange fechada")
        except Exception as e:
            logger.error(f"❌ Erro fechando exchange: {e}")
    
    def get_status(self) -> Dict:
        """Retorna status atual do gerenciador"""
        return {
            'connected': self.is_connected,
            'simulation_mode': self.simulation_mode,
            'exchange_name': self.exchange.name if self.exchange else 'none',
            'active_config': self.config.get('active_config', 'unknown'),
            'supported_symbols': list(self.simulated_prices.keys())
        }

async def test_exchange_manager():
    """Teste do gerenciador de exchange"""
    manager = QuantumExchangeManager()
    
    try:
        # Inicializar
        await manager.initialize()
        
        # Verificar status
        status = manager.get_status()
        print(f"📊 Status: {status}")
        
        # Testar dados de mercado
        symbols = ['BTC/USDT', 'ETH/USDT']
        for symbol in symbols:
            data = await manager.get_market_data(symbol)
            print(f"💱 {symbol}: ${data['price']:.2f} ({data['source']})")
        
        # Testar saldo
        balance = await manager.get_balance()
        print(f"💰 Saldo: {balance}")
        
        # Fechar
        await manager.close()
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")

if __name__ == "__main__":
    asyncio.run(test_exchange_manager())
