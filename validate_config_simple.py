#!/usr/bin/env python3
"""
QUALIA Configuration Validation Script (Simplified)
===================================================

Script simplificado para validar configuração sem dependências complexas.

Execução:
    python validate_config_simple.py

Autor: YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import sys
import yaml
import logging
from pathlib import Path
import importlib.util

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config_manager():
    """Carrega o config_manager diretamente sem dependências"""
    config_manager_path = Path('src/qualia/config_manager.py')
    
    if not config_manager_path.exists():
        raise FileNotFoundError(f"Config manager não encontrado: {config_manager_path.absolute()}")
    
    # Importação direta via importlib
    spec = importlib.util.spec_from_file_location("config_manager", config_manager_path)
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    
    return config_module.get_config_manager, config_module.ConfigurationError

def validate_yaml_directly():
    """Valida o arquivo YAML diretamente"""
    config_path = Path('qualia_config.yaml')
    
    if not config_path.exists():
        print(f"❌ Arquivo de configuração não encontrado: {config_path.absolute()}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        print(f"✅ Arquivo YAML carregado: {config_path.absolute()}")
        print(f"   Tamanho: {config_path.stat().st_size} bytes")
        
        # Verificar estrutura básica
        required_sections = ['quantum_thresholds', 'trading', 'risk_management']
        for section in required_sections:
            if section in config:
                print(f"✅ Seção '{section}' encontrada")
            else:
                print(f"❌ Seção '{section}' ausente")
                return False
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ Erro ao parsear YAML: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def main():
    """Executa validação simplificada da configuração QUALIA"""
    
    print("=" * 80)
    print("🔍 VALIDAÇÃO SIMPLIFICADA DE CONFIGURAÇÃO QUALIA")
    print("=" * 80)
    
    try:
        # 1. Validar YAML diretamente
        print("\n1️⃣ Validando arquivo YAML...")
        if not validate_yaml_directly():
            return False
        
        # 2. Testar carregamento do config manager
        print("\n2️⃣ Testando Config Manager...")
        try:
            get_config_manager, ConfigurationError = load_config_manager()
            print("✅ Config Manager importado com sucesso")
            
            # Testar inicialização
            config_manager = get_config_manager('qualia_config.yaml')
            print("✅ Config Manager inicializado com sucesso")
            
        except Exception as e:
            print(f"❌ Erro no Config Manager: {e}")
            return False
        
        # 3. Validar parâmetros críticos
        print("\n3️⃣ Validando parâmetros críticos...")
        
        critical_params = [
            'quantum_thresholds.consciousness',
            'quantum_thresholds.coherence',
            'quantum_thresholds.confidence',
            'quantum_thresholds.momentum_min',
            'quantum_thresholds.volume_surge_min'
        ]
        
        for param in critical_params:
            try:
                value = config_manager.get(param)
                print(f"✅ {param}: {value}")
            except Exception as e:
                print(f"❌ {param}: ERRO - {e}")
                return False
        
        # 4. Validar configurações por modo
        print("\n4️⃣ Validando modos de trading...")
        
        try:
            trading_modes = config_manager.get('quantum_thresholds.trading_modes')
            
            for mode in ['conservative', 'moderate', 'aggressive']:
                if mode in trading_modes:
                    mode_config = trading_modes[mode]
                    momentum = mode_config.get('momentum_min', 'N/A')
                    print(f"✅ Modo {mode}: momentum_min = {momentum}")
                else:
                    print(f"❌ Modo {mode}: não encontrado")
                    return False
                    
        except Exception as e:
            print(f"❌ Erro validando modos: {e}")
            return False
        
        # 5. Verificar arquivos do sistema
        print("\n5️⃣ Verificando arquivos do sistema...")
        
        system_files = [
            'src/qualia/binance_corrected_system.py',
            'qualia_adaptive_threshold_system.py'
        ]
        
        for file_path in system_files:
            path = Path(file_path)
            if path.exists():
                print(f"✅ {file_path}")
            else:
                print(f"⚠️ {file_path} (não encontrado)")
        
        # 6. Resumo final
        print("\n" + "=" * 80)
        print("🎉 VALIDAÇÃO SIMPLIFICADA CONCLUÍDA COM SUCESSO!")
        print("=" * 80)
        print("✅ Arquivo YAML válido e carregável")
        print("✅ Config Manager funcional")
        print("✅ Parâmetros críticos presentes")
        print("✅ Configurações por modo válidas")
        print("✅ Sistema pronto para carregamento obrigatório")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO INESPERADO:")
        print(f"   {e}")
        print(f"   Tipo: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
