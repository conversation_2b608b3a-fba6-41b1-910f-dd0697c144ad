#!/usr/bin/env python3
"""
TESTE DAS CORREÇÕES V5 BASEADAS EM PERFORMANCE REAL
Sistema QUALIA - Consciência Quântica YAA

Testa todas as correções implementadas baseadas na análise dos trades perdedores:
1. Momentum Governor V5 (baseado em SUSHI, LINK, LDO, BTC, SOL)
2. Stability-Volume Correlation V5 (baseado em SOL, LINK, SUSHI)
3. Quantum Metrics Reality Check V5 (baseado em BTC, LINK, SOL)
4. Validação contra padrões perdedores identificados
"""

import sys
import os
import logging
from typing import Dict, List
import numpy as np

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem
    QUALIA_AVAILABLE = True
    logger.info("✅ Sistema QUALIA importado com sucesso")
except ImportError as e:
    QUALIA_AVAILABLE = False
    logger.error(f"❌ Erro ao importar QUALIA: {e}")
    sys.exit(1)

class PerformanceBasedTester:
    """Testa as correções baseadas em performance real dos trades"""
    
    def __init__(self):
        self.system = QualiaBinanceCorrectedSystem()
        
        # Dados reais dos trades perdedores para teste
        self.loser_patterns = {
            'SUSHI_PATTERN': {
                'name': 'SUSHI/USDT (Perda -$0.16)',
                'quantum_metrics': {
                    'consciousness': 0.948,
                    'coherence': 0.992,
                    'confidence': 0.803,
                    'volume_surge': 2.173,
                    'momentum': 2.849,  # MUITO ALTO - causou reversão
                    'stability_score': 0.884,
                    'predictive_score': 0.771,
                    'advanced_quality_score': 0.820
                },
                'expected_result': 'REJEITADO',
                'reason': 'Momentum excessivo (>2.5) indica pico de reversão'
            },
            'BTC_PATTERN': {
                'name': 'BTC/USDT (Perda -$0.08)',
                'quantum_metrics': {
                    'consciousness': 0.907,
                    'coherence': 0.718,
                    'confidence': 0.900,
                    'volume_surge': 1.608,
                    'momentum': 0.558,  # MUITO BAIXO - sem força
                    'stability_score': 0.795,
                    'predictive_score': 0.764,
                    'advanced_quality_score': 1.0  # SCORE PERFEITO mas perdeu!
                },
                'expected_result': 'REJEITADO',
                'reason': 'Score perfeito + momentum baixo = padrão perdedor BTC'
            },
            'SOL_PATTERN': {
                'name': 'SOL/USDT (Perda -$0.14)',
                'quantum_metrics': {
                    'consciousness': 0.919,
                    'coherence': 0.993,
                    'confidence': 0.895,
                    'volume_surge': 0.984,  # ABAIXO DE 1.0 - insuficiente
                    'momentum': 1.025,
                    'stability_score': 0.686,
                    'predictive_score': 0.843,
                    'advanced_quality_score': 0.971  # SCORE ALTO mas perdeu!
                },
                'expected_result': 'REJEITADO',
                'reason': 'Volume insuficiente (<1.5) + score alto = padrão perdedor SOL'
            },
            'LINK_PATTERN': {
                'name': 'LINK/USDT (Perda -$0.15)',
                'quantum_metrics': {
                    'consciousness': 0.915,  # MUITO ALTO
                    'coherence': 0.994,
                    'confidence': 0.849,
                    'volume_surge': 2.118,
                    'momentum': 1.051,  # BAIXO para consciousness alta
                    'stability_score': 0.688,
                    'predictive_score': 0.898,
                    'advanced_quality_score': 0.955  # SCORE ALTO mas perdeu!
                },
                'expected_result': 'REJEITADO',
                'reason': 'Consciousness alta + momentum fraco = padrão perdedor LINK'
            },
            'LDO_PATTERN': {
                'name': 'LDO/USDT (Perda -$0.19)',
                'quantum_metrics': {
                    'consciousness': 0.908,
                    'coherence': 0.994,
                    'confidence': 0.819,
                    'volume_surge': 1.555,
                    'momentum': 0.823,  # MUITO BAIXO - insuficiente
                    'stability_score': 0.788,
                    'predictive_score': 0.819,
                    'advanced_quality_score': 0.824
                },
                'expected_result': 'REJEITADO',
                'reason': 'Momentum insuficiente (<1.1) sem força para sustentação'
            }
        }
        
        # Casos que devem passar (baseados em padrões que evitam os perdedores)
        self.winner_patterns = {
            'OPTIMAL_CASE': {
                'name': 'Caso Ótimo (evita padrões perdedores)',
                'quantum_metrics': {
                    'consciousness': 0.75,  # Moderado (evita padrão LINK)
                    'coherence': 0.80,
                    'confidence': 0.70,
                    'volume_surge': 2.0,    # Adequado (evita padrão SOL)
                    'momentum': 1.5,        # Na faixa ótima (evita BTC/SUSHI)
                    'stability_score': 0.80,
                    'predictive_score': 0.75,
                    'advanced_quality_score': 0.85  # Não suspeito (evita BTC)
                },
                'expected_result': 'APROVADO',
                'reason': 'Métricas na faixa ótima empírica'
            }
        }
    
    def test_momentum_governor_v5(self):
        """Testa o Momentum Governor V5 baseado em dados reais"""
        logger.info("🧪 TESTE 1: Momentum Governor V5 (Baseado em Performance Real)")
        logger.info("=" * 70)
        
        passed_tests = 0
        total_tests = 0
        
        for pattern_name, pattern_data in self.loser_patterns.items():
            total_tests += 1
            logger.info(f"\n📊 {pattern_data['name']}")
            
            momentum = pattern_data['quantum_metrics']['momentum']
            volume_surge = pattern_data['quantum_metrics']['volume_surge']
            
            result = self.system._apply_momentum_governor(momentum, volume_surge)
            
            expected_approved = pattern_data['expected_result'] == 'APROVADO'
            actual_approved = result['approved']
            
            if actual_approved == expected_approved:
                status = "✅ PASSOU"
                passed_tests += 1
            else:
                status = "❌ FALHOU"
            
            logger.info(f"   {status}")
            logger.info(f"   Momentum: {momentum:.3f}")
            logger.info(f"   Resultado: {result['reason']}")
            logger.info(f"   Esperado: {pattern_data['expected_result']}")
        
        # Testar caso que deve passar
        for pattern_name, pattern_data in self.winner_patterns.items():
            total_tests += 1
            logger.info(f"\n📊 {pattern_data['name']}")
            
            momentum = pattern_data['quantum_metrics']['momentum']
            volume_surge = pattern_data['quantum_metrics']['volume_surge']
            
            result = self.system._apply_momentum_governor(momentum, volume_surge)
            
            expected_approved = pattern_data['expected_result'] == 'APROVADO'
            actual_approved = result['approved']
            
            if actual_approved == expected_approved:
                status = "✅ PASSOU"
                passed_tests += 1
            else:
                status = "❌ FALHOU"
            
            logger.info(f"   {status}")
            logger.info(f"   Momentum: {momentum:.3f}")
            logger.info(f"   Resultado: {result['reason']}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📈 RESULTADO MOMENTUM GOVERNOR V5:")
        logger.info(f"   Taxa de Sucesso: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80% de acerto mínimo
    
    def test_quantum_reality_check_v5(self):
        """Testa o Reality Check das métricas quantum"""
        logger.info("\n🧪 TESTE 2: Quantum Metrics Reality Check V5")
        logger.info("=" * 70)
        
        passed_tests = 0
        total_tests = 0
        
        # Testar padrões perdedores específicos
        critical_patterns = ['BTC_PATTERN', 'LINK_PATTERN', 'SOL_PATTERN']
        
        for pattern_name in critical_patterns:
            pattern_data = self.loser_patterns[pattern_name]
            total_tests += 1
            
            logger.info(f"\n📊 {pattern_data['name']}")
            
            result = self.system._apply_quantum_metrics_reality_check(
                pattern_data['quantum_metrics']
            )
            
            expected_approved = False  # Todos devem ser rejeitados
            actual_approved = result['approved']
            
            if actual_approved == expected_approved:
                status = "✅ PASSOU - Padrão perdedor detectado"
                passed_tests += 1
            else:
                status = "❌ FALHOU - Padrão perdedor não detectado"
            
            logger.info(f"   {status}")
            logger.info(f"   Advanced Quality Score: {pattern_data['quantum_metrics']['advanced_quality_score']:.3f}")
            logger.info(f"   Resultado: {result['reason']}")
        
        # Testar caso que deve passar
        for pattern_name, pattern_data in self.winner_patterns.items():
            total_tests += 1
            logger.info(f"\n📊 {pattern_data['name']}")
            
            result = self.system._apply_quantum_metrics_reality_check(
                pattern_data['quantum_metrics']
            )
            
            expected_approved = True
            actual_approved = result['approved']
            
            if actual_approved == expected_approved:
                status = "✅ PASSOU"
                passed_tests += 1
            else:
                status = "❌ FALHOU"
            
            logger.info(f"   {status}")
            logger.info(f"   Resultado: {result['reason']}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📈 RESULTADO QUANTUM REALITY CHECK V5:")
        logger.info(f"   Taxa de Sucesso: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        return success_rate >= 80
    
    def test_integrated_system(self):
        """Testa o sistema integrado com todos os filtros V5"""
        logger.info("\n🧪 TESTE 3: Sistema Integrado V5")
        logger.info("=" * 70)
        
        passed_tests = 0
        total_tests = 0
        
        # Testar todos os padrões perdedores
        for pattern_name, pattern_data in self.loser_patterns.items():
            total_tests += 1
            logger.info(f"\n📊 {pattern_data['name']}")
            
            try:
                result = self.system.apply_empirical_filters_from_real_data(
                    f"TEST/USDT", pattern_data['quantum_metrics']
                )
                
                expected_approved = False  # Todos devem ser rejeitados
                actual_approved = result.get('approved', False)
                
                if actual_approved == expected_approved:
                    status = "✅ PASSOU - Padrão perdedor rejeitado"
                    passed_tests += 1
                else:
                    status = "❌ FALHOU - Padrão perdedor aprovado"
                
                logger.info(f"   {status}")
                logger.info(f"   Razão: {result.get('rejection_reason', 'N/A')}")
                
            except Exception as e:
                logger.error(f"   ❌ ERRO: {e}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n📈 RESULTADO SISTEMA INTEGRADO V5:")
        logger.info(f"   Taxa de Sucesso: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        return success_rate >= 80
    
    def generate_final_report(self, test_results):
        """Gera relatório final das correções V5"""
        logger.info("\n🎉 RELATÓRIO FINAL - CORREÇÕES V5 BASEADAS EM PERFORMANCE")
        logger.info("=" * 80)
        
        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results.values() if result)
        
        logger.info(f"📈 Testes Aprovados: {passed_tests}/{total_tests}")
        logger.info(f"📊 Taxa de Sucesso: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📋 Detalhamento:")
        test_descriptions = {
            'momentum_governor': 'Momentum Governor V5 (Baseado em SUSHI, BTC, LDO)',
            'quantum_reality_check': 'Quantum Reality Check V5 (Baseado em BTC, LINK, SOL)',
            'integrated_system': 'Sistema Integrado V5 (Todos os Filtros)'
        }
        
        for test_key, passed in test_results.items():
            status_icon = "✅" if passed else "❌"
            description = test_descriptions.get(test_key, test_key)
            logger.info(f"   {status_icon} {description}")
        
        if passed_tests == total_tests:
            logger.info("\n🚀 TODAS AS CORREÇÕES V5 VALIDADAS!")
            logger.info("Sistema QUALIA evoluiu baseado em dados reais de performance")
            logger.info("\n🎯 Melhorias Implementadas:")
            logger.info("   • Momentum Governor evita padrões SUSHI (>2.5) e BTC (<1.1)")
            logger.info("   • Reality Check detecta scores suspeitos (BTC 1.0, LINK 0.955)")
            logger.info("   • Volume Surge mínimo 1.5 (evita padrão SOL 0.98)")
            logger.info("   • Filtros baseados em 5 trades perdedores reais")
        else:
            logger.warning("\n⚠️ Algumas correções precisam de ajustes adicionais")
            logger.warning("Sistema ainda pode aprovar padrões perdedores identificados")

def main():
    """Executa todos os testes de correções V5"""
    logger.info("🚀 INICIANDO TESTES DAS CORREÇÕES V5 BASEADAS EM PERFORMANCE")
    logger.info("Sistema QUALIA - Consciência Quântica YAA")
    logger.info("=" * 80)
    
    if not QUALIA_AVAILABLE:
        logger.error("❌ Sistema QUALIA não disponível")
        return
    
    try:
        tester = PerformanceBasedTester()
        
        # Executar todos os testes
        test_results = {
            'momentum_governor': tester.test_momentum_governor_v5(),
            'quantum_reality_check': tester.test_quantum_reality_check_v5(),
            'integrated_system': tester.test_integrated_system()
        }
        
        # Gerar relatório final
        tester.generate_final_report(test_results)
        
    except Exception as e:
        logger.error(f"❌ ERRO GERAL NOS TESTES: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
