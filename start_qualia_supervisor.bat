@echo off
echo ========================================
echo  QUALIA MULTI-INSTANCE SUPERVISOR
echo ========================================
echo.
echo Iniciando sistema de multiplas instancias...
echo - 2 instancias simultaneas do QUALIA
echo - Nova instancia a cada 21 minutos
echo - Monitoramento independente
echo - Logs detalhados
echo.
echo Pressione Ctrl+C para parar
echo ========================================
echo.

REM Verificar se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Instale Python 3.8+ e tente novamente.
    pause
    exit /b 1
)

REM Verificar se o arquivo supervisor existe
if not exist "qualia_supervisor.py" (
    echo ERRO: qualia_supervisor.py nao encontrado!
    echo Certifique-se de estar no diretorio correto.
    pause
    exit /b 1
)

REM Executar supervisor
python qualia_supervisor.py

echo.
echo Supervisor finalizado.
pause
