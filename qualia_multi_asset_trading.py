#!/usr/bin/env python3
"""
QUALIA - Sistema de Trading Multi-Ativos
Expansão para múltiplos ativos com parâmetros adaptativos

ATIVOS SUPORTADOS:
✅ Criptomoedas Major: BTC, ETH, BNB, ADA, SOL
✅ Criptomoedas Mid-Cap: XMR, LINK, DOT, AVAX
✅ Stablecoins Pairs: USDT, USDC, BUSD
✅ Parâmetros adaptativos por categoria de ativo
✅ Seleção dinâmica baseada em condições de mercado
"""

import asyncio
import ccxt
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import os
from dotenv import load_dotenv
import json

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_multi_asset_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AssetConfig:
    """Configuração específica por categoria de ativo"""
    
    def __init__(self, category: str, volatility_factor: float, liquidity_factor: float):
        self.category = category
        self.volatility_factor = volatility_factor
        self.liquidity_factor = liquidity_factor
        
        # Parâmetros adaptativos baseados na categoria
        if category == "major_crypto":
            self.consciousness_threshold = 0.6
            self.coherence_threshold = 0.55
            self.confidence_threshold = 0.6
            self.max_position_pct = 0.8
        elif category == "mid_cap_crypto":
            self.consciousness_threshold = 0.65  # Mais rigoroso
            self.coherence_threshold = 0.6
            self.confidence_threshold = 0.65
            self.max_position_pct = 0.6  # Menor exposição
        elif category == "alt_crypto":
            self.consciousness_threshold = 0.7   # Muito rigoroso
            self.coherence_threshold = 0.65
            self.confidence_threshold = 0.7
            self.max_position_pct = 0.4  # Exposição mínima
        else:  # stable_pairs
            self.consciousness_threshold = 0.55  # Mais permissivo
            self.coherence_threshold = 0.5
            self.confidence_threshold = 0.55
            self.max_position_pct = 0.9

class MultiAssetTradingSystem:
    """Sistema de trading para múltiplos ativos"""
    
    def __init__(self, phase: int = 1):
        # Configurações por fase
        self.phases = {
            1: {'capital': 50, 'max_loss': 5, 'max_position': 15},
            2: {'capital': 200, 'max_loss': 20, 'max_position': 60},
            3: {'capital': 500, 'max_loss': 50, 'max_position': 150}
        }
        
        self.current_phase = phase
        self.phase_config = self.phases[phase]
        
        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')
        
        # Estado
        self.exchange = None
        self.trades = []
        self.running = False
        self.daily_loss = 0.0
        self.trades_today = 0
        
        # CONFIGURAÇÃO DE ATIVOS EXPANDIDA
        self.asset_universe = {
            # Criptomoedas Major (alta liquidez, menor risco)
            "major_crypto": {
                "symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
                "config": AssetConfig("major_crypto", 1.0, 1.0)
            },

            # Criptomoedas Mid-Cap (liquidez média, risco médio)
            "mid_cap_crypto": {
                "symbols": ["ADA/USDT", "SOL/USDT", "XMR/USDT", "LINK/USDT", "DOT/USDT", "MATIC/USDT"],
                "config": AssetConfig("mid_cap_crypto", 1.5, 0.8)
            },

            # Altcoins (menor liquidez, maior risco)
            "alt_crypto": {
                "symbols": ["AVAX/USDT", "ATOM/USDT", "FTM/USDT", "NEAR/USDT", "ALGO/USDT", "VET/USDT"],
                "config": AssetConfig("alt_crypto", 2.0, 0.6)
            },

            # DeFi Tokens (alta volatilidade, oportunidades específicas)
            "defi_tokens": {
                "symbols": ["UNI/USDT", "AAVE/USDT", "COMP/USDT", "SUSHI/USDT"],
                "config": AssetConfig("alt_crypto", 2.5, 0.5)
            }
        }
        
        # Lista consolidada de símbolos ativos
        self.active_symbols = []
        for category in self.asset_universe.values():
            self.active_symbols.extend(category["symbols"])
        
        logger.info(f"🚀 QUALIA Multi-Asset Trading - Fase {phase}")
        logger.info(f"💰 Capital: ${self.phase_config['capital']}")
        logger.info(f"📊 Ativos: {len(self.active_symbols)} símbolos em {len(self.asset_universe)} categorias")
    
    async def initialize(self):
        """Inicializa sistema multi-asset"""
        try:
            # Conectar à exchange
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            # Testar conexão
            markets = self.exchange.load_markets()
            balance = self.exchange.fetch_balance()
            
            usdt_free = balance.get('USDT', {}).get('free', 0)
            
            logger.info(f"✅ Conectado à KuCoin - {len(markets)} mercados")
            logger.info(f"💰 USDT disponível: ${usdt_free:.2f}")
            
            # Verificar disponibilidade dos símbolos
            available_symbols = []
            for symbol in self.active_symbols:
                if symbol in markets:
                    available_symbols.append(symbol)
                    logger.info(f"✅ {symbol} disponível")
                else:
                    logger.warning(f"⚠️ {symbol} não disponível")
            
            self.active_symbols = available_symbols
            logger.info(f"📊 {len(self.active_symbols)} símbolos ativos confirmados")
            
            # Verificar capital suficiente
            if usdt_free < self.phase_config['capital']:
                logger.error(f"❌ Capital insuficiente: ${usdt_free:.2f} < ${self.phase_config['capital']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}")
            return False
    
    def get_asset_config(self, symbol: str) -> AssetConfig:
        """Obtém configuração específica do ativo"""
        for category_name, category_data in self.asset_universe.items():
            if symbol in category_data["symbols"]:
                return category_data["config"]
        
        # Default para major_crypto se não encontrado
        return self.asset_universe["major_crypto"]["config"]
    
    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de mercado para qualquer ativo"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            
            return {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'spread': ticker['ask'] - ticker['bid'],
                'volume': ticker['baseVolume'],
                'change_24h': ticker['percentage'] / 100 if ticker['percentage'] else 0,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ Erro obtendo dados {symbol}: {e}")
            return None
    
    def calculate_adaptive_quantum_signal(self, market_data: Dict) -> Dict:
        """Calcula sinal quântico adaptativo baseado no tipo de ativo"""
        
        symbol = market_data['symbol']
        asset_config = self.get_asset_config(symbol)
        
        # Métricas base
        volatility = abs(market_data['change_24h'])
        spread_pct = market_data['spread'] / market_data['price']
        volume_strength = min(market_data['volume'] / 1000, 1.0)
        
        # Aplicar fatores de ajuste por categoria de ativo
        volatility_adjusted = volatility * asset_config.volatility_factor
        volume_adjusted = volume_strength * asset_config.liquidity_factor
        
        # Coerência quântica adaptativa
        coherence_base = 0.75 - volatility_adjusted * 8 - spread_pct * 400
        coherence = max(0.0, min(1.0, coherence_base))
        
        # Consciência adaptativa
        consciousness_base = 0.70 + volume_adjusted * 0.25 - volatility_adjusted * 6
        consciousness = max(0.0, min(1.0, consciousness_base))
        
        # Proteção retrocausal específica por ativo
        retrocausal_base = 0.65 - spread_pct * 200 - volatility_adjusted * 5
        retrocausal_protection = max(0.0, min(1.0, retrocausal_base))
        
        # Confiança geral
        confidence = (coherence + consciousness + retrocausal_protection) / 3
        
        # Verificar thresholds específicos do ativo
        thresholds_met = {
            'consciousness': consciousness >= asset_config.consciousness_threshold,
            'coherence': coherence >= asset_config.coherence_threshold,
            'confidence': confidence >= asset_config.confidence_threshold
        }
        
        # Gerar sinal adaptativo
        if all(thresholds_met.values()):
            # Lógica específica por categoria
            if asset_config.category == "major_crypto":
                # Mais permissivo para majors
                if market_data['change_24h'] > -0.03:
                    action = 'buy' if consciousness > coherence else 'hold'
                else:
                    action = 'hold'
            elif asset_config.category == "mid_cap_crypto":
                # Moderadamente conservador
                if market_data['change_24h'] > -0.02 and retrocausal_protection > 0.6:
                    action = 'buy' if consciousness > coherence * 1.1 else 'hold'
                else:
                    action = 'hold'
            else:  # alt_crypto
                # Muito conservador
                if market_data['change_24h'] > -0.01 and retrocausal_protection > 0.7:
                    action = 'buy' if consciousness > coherence * 1.2 else 'hold'
                else:
                    action = 'hold'
        else:
            action = 'hold'
        
        # Log detalhado
        logger.info(f"🧠 {symbol} ({asset_config.category}):")
        logger.info(f"   Coerência: {coherence:.3f} (min: {asset_config.coherence_threshold})")
        logger.info(f"   Consciência: {consciousness:.3f} (min: {asset_config.consciousness_threshold})")
        logger.info(f"   Confiança: {confidence:.3f} (min: {asset_config.confidence_threshold})")
        logger.info(f"   Sinal: {action.upper()}")
        
        return {
            'action': action,
            'confidence': confidence,
            'coherence': coherence,
            'consciousness': consciousness,
            'retrocausal_protection': retrocausal_protection,
            'asset_config': asset_config,
            'market_data': market_data,
            'thresholds_met': thresholds_met
        }
    
    async def execute_adaptive_trade(self, signal: Dict) -> Optional[Dict]:
        """Executa trade com parâmetros adaptativos por ativo"""
        
        if signal['action'] == 'hold':
            return None
        
        try:
            # Verificações de segurança
            if self.daily_loss <= -self.phase_config['max_loss']:
                logger.warning("🚫 Limite diário de perda atingido")
                return None
            
            if self.trades_today >= 20:  # Limite aumentado para multi-asset
                logger.warning("🚫 Limite de trades diários atingido")
                return None
            
            # Calcular tamanho da posição adaptativo
            asset_config = signal['asset_config']
            confidence = signal['confidence']
            
            # Posição base ajustada por categoria
            base_position = self.phase_config['max_position'] / len(self.active_symbols)  # Dividir entre ativos
            position_size = base_position * confidence * asset_config.max_position_pct
            
            symbol = signal['market_data']['symbol']
            price = signal['market_data']['price']
            
            logger.info(f"🎯 EXECUTANDO TRADE MULTI-ASSET:")
            logger.info(f"   Símbolo: {symbol}")
            logger.info(f"   Categoria: {asset_config.category}")
            logger.info(f"   Tamanho: ${position_size:.2f}")
            logger.info(f"   Preço: ${price:.2f}")
            logger.info(f"   Confiança: {confidence:.3f}")
            
            # Calcular quantidade
            quantity = position_size / price
            
            # EXECUTAR ORDEM REAL
            if signal['action'] == 'buy':
                order = self.exchange.create_market_buy_order(symbol, quantity)
            else:
                logger.warning("⚠️ Venda não implementada nesta versão")
                return None
            
            if order and order.get('status') == 'closed':
                # Processar ordem executada
                filled_qty = order['filled']
                avg_price = order['average'] or price
                total_cost = filled_qty * avg_price
                fee = order.get('fee', {}).get('cost', 0) or total_cost * 0.001
                
                # Registrar trade
                trade_record = {
                    'id': order['id'],
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'category': asset_config.category,
                    'action': signal['action'],
                    'quantity': filled_qty,
                    'price': avg_price,
                    'total_cost': total_cost,
                    'fee': fee,
                    'confidence': confidence,
                    'quantum_metrics': {
                        'coherence': signal['coherence'],
                        'consciousness': signal['consciousness'],
                        'retrocausal_protection': signal['retrocausal_protection']
                    },
                    'order': order
                }
                
                # Atualizar contadores
                self.daily_loss -= (total_cost + fee)
                self.trades_today += 1
                self.trades.append(trade_record)
                
                logger.info(f"✅ TRADE MULTI-ASSET EXECUTADO:")
                logger.info(f"   ID: {order['id']}")
                logger.info(f"   Quantidade: {filled_qty:.6f}")
                logger.info(f"   Custo total: ${total_cost:.2f}")
                logger.info(f"   Fee: ${fee:.2f}")
                
                return trade_record
            else:
                logger.error(f"❌ Falha na execução: {order}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro executando trade multi-asset: {e}")
            return None
    
    async def run_multi_asset_session(self, duration_minutes: int = 30):
        """Executa sessão de trading multi-asset"""
        
        logger.info(f"🚀 INICIANDO SESSÃO MULTI-ASSET - {duration_minutes} minutos")
        logger.info(f"📊 Monitorando {len(self.active_symbols)} ativos")
        logger.info(f"💰 Capital: ${self.phase_config['capital']}")
        
        self.running = True
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        cycle = 0
        
        try:
            while datetime.now() < end_time and self.running:
                cycle += 1
                
                logger.info(f"🔄 Ciclo {cycle} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Processar todos os ativos
                for symbol in self.active_symbols:
                    if not self.running:
                        break
                    
                    # Obter dados de mercado
                    market_data = await self.get_market_data(symbol)
                    if not market_data:
                        continue
                    
                    # Calcular sinal adaptativo
                    signal = self.calculate_adaptive_quantum_signal(market_data)
                    
                    # Executar trade se sinal válido
                    if signal['action'] != 'hold':
                        trade_result = await self.execute_adaptive_trade(signal)
                        
                        if trade_result:
                            logger.info(f"💎 Trade {symbol} executado!")
                            # Pausa após trade
                            await asyncio.sleep(15)
                
                # Status geral
                trades_by_category = {}
                for trade in self.trades:
                    cat = trade['category']
                    trades_by_category[cat] = trades_by_category.get(cat, 0) + 1
                
                logger.info(f"🛡️ Status Multi-Asset:")
                logger.info(f"   Perda diária: ${self.daily_loss:.2f}")
                logger.info(f"   Trades hoje: {self.trades_today}")
                logger.info(f"   Por categoria: {trades_by_category}")
                
                # Aguardar próximo ciclo
                await asyncio.sleep(45)  # 45 segundos para processar mais ativos
                
        except KeyboardInterrupt:
            logger.info("🛑 Sessão multi-asset interrompida pelo usuário")
            self.running = False
        except Exception as e:
            logger.error(f"❌ Erro na sessão multi-asset: {e}")
        
        return self.generate_multi_asset_report()
    
    def generate_multi_asset_report(self) -> Dict:
        """Gera relatório multi-asset"""
        
        total_trades = len(self.trades)
        total_invested = sum(t['total_cost'] + t['fee'] for t in self.trades)
        
        # Análise por categoria
        category_analysis = {}
        for category_name in self.asset_universe.keys():
            category_trades = [t for t in self.trades if t['category'] == category_name]
            if category_trades:
                category_analysis[category_name] = {
                    'trades': len(category_trades),
                    'total_invested': sum(t['total_cost'] + t['fee'] for t in category_trades),
                    'avg_confidence': sum(t['confidence'] for t in category_trades) / len(category_trades)
                }
        
        # Análise por símbolo
        symbol_analysis = {}
        for symbol in self.active_symbols:
            symbol_trades = [t for t in self.trades if t['symbol'] == symbol]
            if symbol_trades:
                symbol_analysis[symbol] = {
                    'trades': len(symbol_trades),
                    'total_invested': sum(t['total_cost'] + t['fee'] for t in symbol_trades)
                }
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'phase': self.current_phase,
            'version': 'MULTI_ASSET_SYSTEM',
            'assets_monitored': len(self.active_symbols),
            'categories': list(self.asset_universe.keys()),
            'summary': {
                'total_trades': total_trades,
                'total_invested': total_invested,
                'daily_loss': self.daily_loss,
                'trades_today': self.trades_today
            },
            'category_analysis': category_analysis,
            'symbol_analysis': symbol_analysis,
            'detailed_trades': self.trades
        }
        
        return report

async def main():
    """Função principal multi-asset"""
    
    print("🌌 QUALIA - SISTEMA DE TRADING MULTI-ATIVOS")
    print("📊 EXPANSÃO PARA MÚLTIPLOS ATIVOS E CATEGORIAS")
    print("=" * 60)
    
    # Mostrar ativos suportados
    print("\n📊 ATIVOS SUPORTADOS:")
    print("🔵 Major Crypto: BTC, ETH, BNB")
    print("🟡 Mid-Cap Crypto: ADA, SOL, XMR, LINK, DOT")
    print("🟠 Alt Crypto: AVAX, ATOM, FTM, NEAR")
    print("\n🔧 PARÂMETROS ADAPTATIVOS POR CATEGORIA")
    
    # Confirmação
    confirm = input("\nDigite 'AUTORIZO MULTI-ASSET' para continuar: ")
    if confirm != 'AUTORIZO MULTI-ASSET':
        print("❌ Operação cancelada")
        return
    
    # Selecionar fase
    phase = int(input("Selecione a fase (1-3): "))
    if phase not in [1, 2, 3]:
        print("❌ Fase inválida")
        return
    
    duration = int(input("Duração em minutos (recomendado 45-90): "))
    
    # Inicializar sistema
    trading_system = MultiAssetTradingSystem(phase)
    
    try:
        # Inicializar
        if not await trading_system.initialize():
            print("❌ Falha na inicialização")
            return
        
        print(f"\n🚀 Iniciando trading multi-asset - Fase {phase}")
        print(f"📊 {len(trading_system.active_symbols)} ativos ativos")
        print("🔴 Pressione Ctrl+C para parar")
        
        # Executar sessão
        report = await trading_system.run_multi_asset_session(duration)
        
        # Mostrar relatório
        print("\n" + "="*60)
        print("📊 RELATÓRIO MULTI-ASSET")
        print("="*60)
        
        summary = report['summary']
        category_analysis = report['category_analysis']
        symbol_analysis = report['symbol_analysis']
        
        print(f"Ativos monitorados: {report['assets_monitored']}")
        print(f"Trades executados: {summary['total_trades']}")
        print(f"Capital investido: ${summary['total_invested']:.2f}")
        print(f"Perda/ganho diário: ${summary['daily_loss']:.2f}")
        
        if category_analysis:
            print("\n📊 ANÁLISE POR CATEGORIA:")
            for category, data in category_analysis.items():
                print(f"  {category}: {data['trades']} trades, ${data['total_invested']:.2f}")
        
        if symbol_analysis:
            print("\n📈 ANÁLISE POR SÍMBOLO:")
            for symbol, data in symbol_analysis.items():
                print(f"  {symbol}: {data['trades']} trades, ${data['total_invested']:.2f}")
        
        # Salvar relatório
        filename = f"qualia_multi_asset_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Relatório salvo: {filename}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
