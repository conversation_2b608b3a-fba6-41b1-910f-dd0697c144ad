#!/usr/bin/env python3
"""
TESTE DE CALIBRAÇÃO V3 - OTIMIZAÇÃO PARA TAXA DE APROVAÇÃO
Sistema QUALIA - Consciência Quântica

Testa as novas calibrações dos filtros anti-falsos positivos
para resolver o problema de baixa taxa de aprovação (4.8% → meta 15-25%)
"""

import sys
import os
import logging
from typing import Dict, List
import numpy as np

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem
    from qualia.empirical_calibrator import QualiaEmpiricalCalibrator
    QUALIA_AVAILABLE = True
    logger.info("✅ Sistema QUALIA importado com sucesso")
except ImportError as e:
    QUALIA_AVAILABLE = False
    logger.error(f"❌ Erro ao importar QUALIA: {e}")
    sys.exit(1)

class TaxaAprovacaoTester:
    """Testa a taxa de aprovação com as novas calibrações V3"""
    
    def __init__(self):
        self.system = QualiaBinanceCorrectedSystem()
        self.calibrator = QualiaEmpiricalCalibrator(self.system)
        self.test_cases = self._generate_test_cases()
        
    def _generate_test_cases(self) -> List[Dict]:
        """Gera casos de teste representativos do mercado real"""
        return [
            # Casos que eram rejeitados pelos filtros antigos
            {
                'name': 'Momentum Alto (era rejeitado)',
                'metrics': {
                    'consciousness': 0.65,
                    'coherence': 0.70,
                    'confidence': 0.60,
                    'momentum': 1.8,  # Era rejeitado por > 1.5
                    'volume_surge': 2.2,
                    'stability': 0.75,
                    'predictive_score': 0.78
                }
            },
            {
                'name': 'Pullback Saudável (era rejeitado)',
                'metrics': {
                    'consciousness': 0.62,
                    'coherence': 0.68,
                    'confidence': 0.58,
                    'momentum': -0.3,  # Era rejeitado por negativo
                    'volume_surge': 1.8,
                    'stability': 0.70,
                    'predictive_score': 0.72
                }
            },
            {
                'name': 'Coherence Alta (era rejeitado)',
                'metrics': {
                    'consciousness': 0.68,
                    'coherence': 0.98,  # Era rejeitado por > 0.97
                    'confidence': 0.65,
                    'momentum': 0.8,  # Era rejeitado por < 1.0 com coherence alta
                    'volume_surge': 2.5,
                    'stability': 0.80,
                    'predictive_score': 0.82
                }
            },
            {
                'name': 'Stability Alta + Volume Moderado (era rejeitado)',
                'metrics': {
                    'consciousness': 0.60,
                    'coherence': 0.72,
                    'confidence': 0.55,
                    'momentum': 1.2,
                    'volume_surge': 2.0,  # Era rejeitado por < 2.5 com stability > 0.8
                    'stability': 0.85,  # Era rejeitado por > 0.8
                    'predictive_score': 0.75
                }
            },
            {
                'name': 'Caso Marginal Baixo',
                'metrics': {
                    'consciousness': 0.52,
                    'coherence': 0.62,
                    'confidence': 0.50,
                    'momentum': 0.5,
                    'volume_surge': 1.2,
                    'stability': 0.68,
                    'predictive_score': 0.58
                }
            },
            {
                'name': 'Caso Extremo (deve ser rejeitado)',
                'metrics': {
                    'consciousness': 0.30,
                    'coherence': 0.40,
                    'confidence': 0.35,
                    'momentum': -1.0,  # Muito negativo
                    'volume_surge': 0.8,
                    'stability': 0.45,
                    'predictive_score': 0.40
                }
            }
        ]
    
    def test_filtros_individuais(self):
        """Testa cada filtro individualmente"""
        logger.info("🧪 TESTE 1: Filtros Individuais (Calibração V3)")
        logger.info("=" * 60)
        
        aprovados_momentum = 0
        aprovados_coherence = 0
        aprovados_stability = 0
        total_casos = len(self.test_cases)
        
        for i, case in enumerate(self.test_cases):
            logger.info(f"\n📊 Caso {i+1}: {case['name']}")
            metrics = case['metrics']
            
            # Teste Momentum Governor
            momentum_result = self.system._apply_momentum_governor(
                metrics['momentum'], metrics['volume_surge']
            )
            status_momentum = "✅" if momentum_result['approved'] else "❌"
            logger.info(f"   {status_momentum} Momentum Governor: {momentum_result['reason']}")
            if momentum_result['approved']:
                aprovados_momentum += 1
            
            # Teste Coherence Cap
            coherence_result = self.system._apply_coherence_cap(
                metrics['coherence'], metrics['momentum']
            )
            status_coherence = "✅" if coherence_result['approved'] else "❌"
            logger.info(f"   {status_coherence} Coherence Cap: {coherence_result['reason']}")
            if coherence_result['approved']:
                aprovados_coherence += 1
            
            # Teste Stability-Volume
            stability_result = self.system._apply_stability_volume_correlation(
                metrics['stability'], metrics['volume_surge']
            )
            status_stability = "✅" if stability_result['approved'] else "❌"
            logger.info(f"   {status_stability} Stability-Volume: {stability_result['reason']}")
            if stability_result['approved']:
                aprovados_stability += 1
        
        # Calcular taxas de aprovação
        taxa_momentum = (aprovados_momentum / total_casos) * 100
        taxa_coherence = (aprovados_coherence / total_casos) * 100
        taxa_stability = (aprovados_stability / total_casos) * 100
        
        logger.info(f"\n📈 RESULTADOS DOS FILTROS INDIVIDUAIS:")
        logger.info(f"   Momentum Governor: {aprovados_momentum}/{total_casos} ({taxa_momentum:.1f}%)")
        logger.info(f"   Coherence Cap: {aprovados_coherence}/{total_casos} ({taxa_coherence:.1f}%)")
        logger.info(f"   Stability-Volume: {aprovados_stability}/{total_casos} ({taxa_stability:.1f}%)")
        
        return {
            'momentum': taxa_momentum,
            'coherence': taxa_coherence,
            'stability': taxa_stability
        }
    
    def test_sistema_completo(self):
        """Testa o sistema completo com todos os filtros"""
        logger.info("\n🧪 TESTE 2: Sistema Completo (Todos os Filtros)")
        logger.info("=" * 60)
        
        aprovados_total = 0
        total_casos = len(self.test_cases)
        
        for i, case in enumerate(self.test_cases):
            logger.info(f"\n📊 Caso {i+1}: {case['name']}")
            
            try:
                # Aplicar filtros empíricos completos
                result = self.system.apply_empirical_filters_from_real_data(
                    f"TEST{i}/USDT", case['metrics']
                )
                
                aprovado = result.get('approved', False)
                status = "✅ APROVADO" if aprovado else "❌ REJEITADO"
                
                logger.info(f"   {status}")
                logger.info(f"   Razão: {result.get('reason', 'N/A')}")
                logger.info(f"   Confiança: {result.get('confidence', 0):.3f}")
                
                if aprovado:
                    aprovados_total += 1
                    
            except Exception as e:
                logger.error(f"   ❌ ERRO: {e}")
        
        # Calcular taxa de aprovação total
        taxa_total = (aprovados_total / total_casos) * 100
        
        logger.info(f"\n📈 RESULTADO FINAL:")
        logger.info(f"   Taxa de Aprovação: {aprovados_total}/{total_casos} ({taxa_total:.1f}%)")
        
        # Avaliar resultado
        if taxa_total >= 15:
            logger.info(f"   🎯 META ATINGIDA! Taxa {taxa_total:.1f}% ≥ 15% (meta mínima)")
        else:
            logger.warning(f"   ⚠️ Abaixo da meta: {taxa_total:.1f}% < 15%")
            
        if taxa_total >= 25:
            logger.info(f"   🚀 EXCELENTE! Taxa {taxa_total:.1f}% ≥ 25% (meta ideal)")
        
        return taxa_total
    
    def test_comparacao_thresholds(self):
        """Compara thresholds antigos vs novos"""
        logger.info("\n🧪 TESTE 3: Comparação de Thresholds")
        logger.info("=" * 60)
        
        # Obter thresholds atuais (V3)
        thresholds_v3 = self.calibrator._initialize_empirical_rules()['validated_thresholds']
        
        logger.info("📊 THRESHOLDS CALIBRAÇÃO V3 (OTIMIZADOS):")
        logger.info(f"   Consciousness: {thresholds_v3['consciousness']} (era 0.55)")
        logger.info(f"   Coherence: {thresholds_v3['coherence']} (era 0.65)")
        logger.info(f"   Confidence: {thresholds_v3['confidence']} (era 0.52)")
        logger.info(f"   Volume Surge Min: {thresholds_v3['volume_surge_min']} (era 0.60)")
        logger.info(f"   Momentum Min: {thresholds_v3['momentum_min']} (era 0.0001)")
        logger.info(f"   Composite Score Min: {thresholds_v3['composite_score_min']} (era 0.75)")
        logger.info(f"   Predictive Score Min: {thresholds_v3['predictive_score_min']} (era 0.65)")
        
        logger.info("\n🎯 MELHORIAS IMPLEMENTADAS:")
        logger.info("   ✅ Momentum Governor: Permite momentum alto com penalidade (não rejeita)")
        logger.info("   ✅ Coherence Cap: Threshold 0.97 → 0.99 (mais permissivo)")
        logger.info("   ✅ Stability-Volume: Thresholds 0.8/2.5 → 0.9/1.8 (mais flexível)")
        logger.info("   ✅ Thresholds gerais reduzidos em 5-15% para mais oportunidades")

def main():
    """Executa todos os testes de calibração V3"""
    logger.info("🚀 INICIANDO TESTES DE CALIBRAÇÃO V3")
    logger.info("Sistema QUALIA - Otimização para Taxa de Aprovação")
    logger.info("=" * 70)
    
    if not QUALIA_AVAILABLE:
        logger.error("❌ Sistema QUALIA não disponível")
        return
    
    try:
        tester = TaxaAprovacaoTester()
        
        # Teste 1: Filtros individuais
        taxas_individuais = tester.test_filtros_individuais()
        
        # Teste 2: Sistema completo
        taxa_total = tester.test_sistema_completo()
        
        # Teste 3: Comparação de thresholds
        tester.test_comparacao_thresholds()
        
        # Resumo final
        logger.info("\n🎉 RESUMO FINAL - CALIBRAÇÃO V3")
        logger.info("=" * 70)
        logger.info(f"📈 Taxa de Aprovação Alcançada: {taxa_total:.1f}%")
        logger.info(f"🎯 Meta Mínima (15%): {'✅ ATINGIDA' if taxa_total >= 15 else '❌ NÃO ATINGIDA'}")
        logger.info(f"🚀 Meta Ideal (25%): {'✅ ATINGIDA' if taxa_total >= 25 else '⚠️ NÃO ATINGIDA'}")
        
        if taxa_total >= 15:
            logger.info("\n✅ CALIBRAÇÃO V3 VALIDADA!")
            logger.info("Sistema pronto para operação com taxa de aprovação adequada")
        else:
            logger.warning("\n⚠️ Calibração precisa de ajustes adicionais")
            logger.warning("Considere relaxar ainda mais os thresholds")
        
    except Exception as e:
        logger.error(f"❌ ERRO GERAL NOS TESTES: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
