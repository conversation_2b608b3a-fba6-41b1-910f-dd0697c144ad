#!/bin/bash

echo "========================================"
echo "  QUALIA MULTI-INSTANCE SUPERVISOR"
echo "========================================"
echo ""
echo "Iniciando sistema de multiplas instancias..."
echo "- 2 instancias simultaneas do QUALIA"
echo "- Nova instancia a cada 21 minutos"
echo "- Monitoramento independente"
echo "- Logs detalhados"
echo ""
echo "Pressione Ctrl+C para parar"
echo "========================================"
echo ""

# Verificar se Python esta instalado
if ! command -v python3 &> /dev/null; then
    echo "ERRO: Python3 nao encontrado!"
    echo "Instale Python 3.8+ e tente novamente."
    exit 1
fi

# Verificar se o arquivo supervisor existe
if [ ! -f "qualia_supervisor.py" ]; then
    echo "ERRO: qualia_supervisor.py nao encontrado!"
    echo "Certifique-se de estar no diretorio correto."
    exit 1
fi

# Tornar o script executavel
chmod +x qualia_supervisor.py

# Executar supervisor
python3 qualia_supervisor.py

echo ""
echo "Supervisor finalizado."
