{"supervisor_settings": {"restart_interval_minutes": 21, "script_path": "src/qualia/binance_corrected_system.py", "max_restart_attempts": 10, "restart_delay_seconds": 30, "process_timeout_seconds": 1260, "monitoring_interval_seconds": 30, "log_retention_days": 7, "enable_status_reports": true, "status_report_interval_minutes": 5}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "max_log_size_mb": 50, "backup_count": 5}, "alerts": {"enable_email_alerts": false, "email_on_failure": false, "email_on_restart": false, "max_failures_before_alert": 3}, "performance": {"monitor_cpu_usage": true, "monitor_memory_usage": true, "cpu_threshold_percent": 90, "memory_threshold_percent": 85, "auto_restart_on_high_usage": false}}