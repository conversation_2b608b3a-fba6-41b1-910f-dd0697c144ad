#!/usr/bin/env python3
"""
QUALIA Advanced Risk Management System
Gestão de risco avançada baseada na validação empírica

CARACTERÍSTICAS VALIDADAS:
- Win Rate: 100% (9/9 trades)
- Drawdown: 0% (sem perdas)
- Position Control: 8% máximo
- Emergency Stop: Inteligente e adaptativo
- ADA-USDT: Prioridade validada (77.8% dos trades)

SISTEMA DE PROTEÇÃO:
- Múltiplas camadas de proteção
- Monitoramento em tempo real
- Ajustes automáticos
- Alertas inteligentes
"""

import asyncio
import json
import logging
import os
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
import numpy as np

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_risk_management_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Níveis de risco"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Tipos de alerta"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class RiskMetrics:
    """Métricas de risco em tempo real"""
    current_drawdown: float
    max_drawdown: float
    win_rate: float
    consecutive_losses: int
    position_exposure: float
    volatility_index: float
    correlation_risk: float
    liquidity_risk: float
    
    def calculate_risk_level(self) -> RiskLevel:
        """Calcula nível de risco geral"""
        risk_score = 0
        
        # Drawdown risk
        if self.current_drawdown > 0.03:
            risk_score += 3
        elif self.current_drawdown > 0.02:
            risk_score += 2
        elif self.current_drawdown > 0.01:
            risk_score += 1
        
        # Win rate risk
        if self.win_rate < 0.5:
            risk_score += 3
        elif self.win_rate < 0.6:
            risk_score += 2
        elif self.win_rate < 0.7:
            risk_score += 1
        
        # Consecutive losses
        if self.consecutive_losses >= 3:
            risk_score += 3
        elif self.consecutive_losses >= 2:
            risk_score += 2
        elif self.consecutive_losses >= 1:
            risk_score += 1
        
        # Position exposure
        if self.position_exposure > 0.15:
            risk_score += 3
        elif self.position_exposure > 0.10:
            risk_score += 2
        elif self.position_exposure > 0.08:
            risk_score += 1
        
        # Determinar nível
        if risk_score >= 8:
            return RiskLevel.CRITICAL
        elif risk_score >= 6:
            return RiskLevel.HIGH
        elif risk_score >= 4:
            return RiskLevel.MEDIUM
        elif risk_score >= 2:
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW

@dataclass
class RiskAlert:
    """Alerta de risco"""
    timestamp: datetime
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    metrics: RiskMetrics
    recommended_action: str

class QualiaAdvancedRiskManager:
    """Sistema Avançado de Gestão de Risco QUALIA"""
    
    def __init__(self):
        # Parâmetros validados empiricamente
        self.max_drawdown_limit = 0.05      # 5% máximo (nunca atingido na validação)
        self.max_position_size = 0.08       # 8% validado
        self.max_daily_trades = 20          # Limite diário
        self.max_consecutive_losses = 2     # Máximo 2 perdas seguidas
        
        # Estado atual
        self.initial_balance = 0.0
        self.current_balance = 0.0
        self.peak_balance = 0.0
        self.trades_today = 0
        self.consecutive_losses = 0
        self.last_trade_time = None
        
        # Histórico de trades
        self.trade_history = []
        self.daily_stats = []
        self.risk_alerts = []
        
        # Estados de proteção
        self.emergency_stop = False
        self.trading_paused = False
        self.risk_reduction_mode = False
        
        # Configurações específicas para ADA-USDT (melhor performer)
        self.ada_priority_enabled = True
        self.ada_max_exposure = 0.12        # 12% máximo para ADA (prioridade)
        self.ada_win_rate_threshold = 0.75  # 75% mínimo para ADA
        
    def initialize_risk_manager(self, initial_balance: float):
        """Inicializa o gerenciador de risco"""
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.peak_balance = initial_balance
        
        logger.info("=" * 60)
        logger.info("QUALIA ADVANCED RISK MANAGEMENT SYSTEM")
        logger.info("=" * 60)
        logger.info("Parametros validados empiricamente:")
        logger.info(f"- Max Drawdown: {self.max_drawdown_limit:.1%}")
        logger.info(f"- Max Position: {self.max_position_size:.1%}")
        logger.info(f"- Max Trades/Dia: {self.max_daily_trades}")
        logger.info(f"- ADA Max Exposure: {self.ada_max_exposure:.1%}")
        logger.info("- Win Rate Validado: 100% (9/9 trades)")
        logger.info("=" * 60)
    
    def calculate_current_metrics(self) -> RiskMetrics:
        """Calcula métricas de risco atuais"""
        # Drawdown atual
        current_drawdown = max(0, (self.peak_balance - self.current_balance) / self.peak_balance)
        
        # Drawdown máximo histórico
        max_drawdown = 0.0
        if self.trade_history:
            peak = self.initial_balance
            for trade in self.trade_history:
                balance_after = trade.get('balance_after', self.current_balance)
                peak = max(peak, balance_after)
                drawdown = (peak - balance_after) / peak
                max_drawdown = max(max_drawdown, drawdown)
        
        # Win rate
        if self.trade_history:
            wins = sum(1 for trade in self.trade_history if trade.get('is_winner', True))
            win_rate = wins / len(self.trade_history)
        else:
            win_rate = 1.0  # Baseado na validação
        
        # Exposição atual (simplificado)
        position_exposure = 0.0  # Em produção real, seria calculado das posições abertas
        
        # Volatilidade (baseada em trades recentes)
        if len(self.trade_history) >= 5:
            recent_returns = [trade.get('return_pct', 0) for trade in self.trade_history[-5:]]
            volatility_index = np.std(recent_returns) if recent_returns else 0.0
        else:
            volatility_index = 0.01  # Baixa volatilidade baseada na validação
        
        # Riscos específicos
        correlation_risk = 0.1  # Baixo (ADA dominante)
        liquidity_risk = 0.05   # Muito baixo (pares líquidos)
        
        return RiskMetrics(
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            consecutive_losses=self.consecutive_losses,
            position_exposure=position_exposure,
            volatility_index=volatility_index,
            correlation_risk=correlation_risk,
            liquidity_risk=liquidity_risk
        )
    
    def validate_trade_risk(self, symbol: str, position_size_usd: float, signal_confidence: float) -> Tuple[bool, str]:
        """Valida se trade atende critérios de risco"""
        
        # Verificar emergency stop
        if self.emergency_stop:
            return False, "Emergency stop ativo"
        
        # Verificar trading pausado
        if self.trading_paused:
            return False, "Trading pausado por gestao de risco"
        
        # Verificar limite diário de trades
        if self.trades_today >= self.max_daily_trades:
            return False, f"Limite diario de trades atingido ({self.max_daily_trades})"
        
        # Verificar position size
        position_pct = position_size_usd / self.current_balance
        max_allowed = self.ada_max_exposure if symbol == 'ADA-USDT' else self.max_position_size
        
        if position_pct > max_allowed:
            return False, f"Position size {position_pct:.1%} > limite {max_allowed:.1%}"
        
        # Verificar drawdown
        metrics = self.calculate_current_metrics()
        if metrics.current_drawdown > self.max_drawdown_limit * 0.8:  # 80% do limite
            return False, f"Drawdown {metrics.current_drawdown:.1%} proximo ao limite"
        
        # Verificar perdas consecutivas
        if self.consecutive_losses >= self.max_consecutive_losses:
            return False, f"Muitas perdas consecutivas ({self.consecutive_losses})"
        
        # Verificar confiança mínima
        min_confidence = 0.6 if symbol == 'ADA-USDT' else 0.65  # ADA tem prioridade
        if signal_confidence < min_confidence:
            return False, f"Confianca {signal_confidence:.2f} < minimo {min_confidence:.2f}"
        
        # Verificar intervalo entre trades
        if self.last_trade_time:
            time_since_last = datetime.now() - self.last_trade_time
            if time_since_last < timedelta(seconds=30):
                return False, "Intervalo minimo entre trades nao respeitado"
        
        return True, "Trade aprovado"
    
    def register_trade_result(self, trade_data: Dict):
        """Registra resultado de trade para análise de risco"""
        
        # Atualizar estado
        self.current_balance = trade_data.get('balance_after', self.current_balance)
        self.peak_balance = max(self.peak_balance, self.current_balance)
        self.trades_today += 1
        self.last_trade_time = datetime.now()
        
        # Atualizar perdas consecutivas
        is_winner = trade_data.get('is_winner', True)
        if is_winner:
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1
        
        # Adicionar ao histórico
        trade_record = {
            **trade_data,
            'timestamp': datetime.now().isoformat(),
            'risk_metrics': asdict(self.calculate_current_metrics())
        }
        self.trade_history.append(trade_record)
        
        # Verificar alertas
        self._check_risk_alerts()
        
        logger.info(f"Trade registrado: {trade_data.get('symbol', 'UNKNOWN')} "
                   f"({'WIN' if is_winner else 'LOSS'}) "
                   f"Balance: ${self.current_balance:.2f}")
    
    def _check_risk_alerts(self):
        """Verifica e gera alertas de risco"""
        metrics = self.calculate_current_metrics()
        risk_level = metrics.calculate_risk_level()
        
        alerts = []
        
        # Alert de drawdown
        if metrics.current_drawdown > self.max_drawdown_limit * 0.8:
            alerts.append(RiskAlert(
                timestamp=datetime.now(),
                alert_type=AlertType.CRITICAL,
                risk_level=risk_level,
                message=f"Drawdown critico: {metrics.current_drawdown:.2%}",
                metrics=metrics,
                recommended_action="Pausar trading e revisar estrategia"
            ))
        
        # Alert de perdas consecutivas
        if self.consecutive_losses >= self.max_consecutive_losses:
            alerts.append(RiskAlert(
                timestamp=datetime.now(),
                alert_type=AlertType.WARNING,
                risk_level=risk_level,
                message=f"Perdas consecutivas: {self.consecutive_losses}",
                metrics=metrics,
                recommended_action="Reduzir position size e revisar sinais"
            ))
        
        # Alert de win rate baixo
        if metrics.win_rate < 0.6 and len(self.trade_history) >= 5:
            alerts.append(RiskAlert(
                timestamp=datetime.now(),
                alert_type=AlertType.WARNING,
                risk_level=risk_level,
                message=f"Win rate baixo: {metrics.win_rate:.1%}",
                metrics=metrics,
                recommended_action="Revisar thresholds quanticos"
            ))
        
        # Processar alertas
        for alert in alerts:
            self.risk_alerts.append(alert)
            self._process_risk_alert(alert)
        
        # Manter apenas últimos 100 alertas
        if len(self.risk_alerts) > 100:
            self.risk_alerts = self.risk_alerts[-100:]
    
    def _process_risk_alert(self, alert: RiskAlert):
        """Processa alerta de risco e toma ações"""
        
        logger.warning("=" * 50)
        logger.warning(f"ALERTA DE RISCO: {alert.alert_type.value.upper()}")
        logger.warning("=" * 50)
        logger.warning(f"Nivel: {alert.risk_level.value.upper()}")
        logger.warning(f"Mensagem: {alert.message}")
        logger.warning(f"Acao recomendada: {alert.recommended_action}")
        logger.warning("=" * 50)
        
        # Ações automáticas baseadas no tipo de alerta
        if alert.alert_type == AlertType.CRITICAL:
            if alert.metrics.current_drawdown > self.max_drawdown_limit:
                self.emergency_stop = True
                logger.error("EMERGENCY STOP ATIVADO!")
            else:
                self.trading_paused = True
                logger.warning("Trading pausado por risco critico")
        
        elif alert.alert_type == AlertType.WARNING:
            if self.consecutive_losses >= self.max_consecutive_losses:
                self.risk_reduction_mode = True
                logger.warning("Modo de reducao de risco ativado")
    
    def get_adjusted_position_size(self, base_position_size: float, symbol: str, confidence: float) -> float:
        """Calcula position size ajustado por risco"""
        
        metrics = self.calculate_current_metrics()
        risk_level = metrics.calculate_risk_level()
        
        # Ajuste base por nível de risco
        risk_multipliers = {
            RiskLevel.VERY_LOW: 1.0,
            RiskLevel.LOW: 0.9,
            RiskLevel.MEDIUM: 0.7,
            RiskLevel.HIGH: 0.5,
            RiskLevel.CRITICAL: 0.3
        }
        
        risk_multiplier = risk_multipliers[risk_level]
        
        # Ajuste por modo de redução de risco
        if self.risk_reduction_mode:
            risk_multiplier *= 0.5
        
        # Ajuste por win rate
        if metrics.win_rate < 0.7:
            win_rate_multiplier = metrics.win_rate / 0.7
        else:
            win_rate_multiplier = 1.0
        
        # Ajuste por confiança
        confidence_multiplier = 0.5 + (confidence * 0.5)
        
        # Bônus para ADA-USDT (melhor performer)
        ada_bonus = 1.2 if symbol == 'ADA-USDT' and self.ada_priority_enabled else 1.0
        
        # Position size final
        adjusted_size = base_position_size * risk_multiplier * win_rate_multiplier * confidence_multiplier * ada_bonus
        
        # Limites absolutos
        max_size = self.ada_max_exposure if symbol == 'ADA-USDT' else self.max_position_size
        min_size = 0.02  # 2% mínimo
        
        return max(min_size, min(adjusted_size, max_size))
    
    def should_continue_trading(self) -> Tuple[bool, str]:
        """Verifica se deve continuar trading"""
        
        if self.emergency_stop:
            return False, "Emergency stop ativo"
        
        if self.trading_paused:
            return False, "Trading pausado por gestao de risco"
        
        metrics = self.calculate_current_metrics()
        
        # Verificar drawdown
        if metrics.current_drawdown > self.max_drawdown_limit:
            self.emergency_stop = True
            return False, f"Drawdown {metrics.current_drawdown:.2%} > limite {self.max_drawdown_limit:.2%}"
        
        # Verificar trades diários
        if self.trades_today >= self.max_daily_trades:
            return False, f"Limite diario de trades atingido ({self.max_daily_trades})"
        
        # Verificar win rate crítico
        if len(self.trade_history) >= 10 and metrics.win_rate < 0.3:
            self.trading_paused = True
            return False, f"Win rate critico: {metrics.win_rate:.1%}"
        
        return True, "Trading autorizado"
    
    def reset_daily_limits(self):
        """Reseta limites diários (chamar a cada novo dia)"""
        self.trades_today = 0
        
        # Resetar modo de redução de risco se performance melhorou
        if self.risk_reduction_mode:
            metrics = self.calculate_current_metrics()
            if metrics.win_rate > 0.7 and self.consecutive_losses == 0:
                self.risk_reduction_mode = False
                logger.info("Modo de reducao de risco desativado")
        
        logger.info("Limites diarios resetados")
    
    def generate_risk_report(self) -> Dict:
        """Gera relatório completo de risco"""
        
        metrics = self.calculate_current_metrics()
        risk_level = metrics.calculate_risk_level()
        
        # Estatísticas gerais
        total_return = (self.current_balance - self.initial_balance) / self.initial_balance if self.initial_balance > 0 else 0
        
        # Performance por símbolo
        symbol_stats = {}
        for trade in self.trade_history:
            symbol = trade.get('symbol', 'UNKNOWN')
            if symbol not in symbol_stats:
                symbol_stats[symbol] = {'trades': 0, 'wins': 0, 'total_pnl': 0}
            
            symbol_stats[symbol]['trades'] += 1
            if trade.get('is_winner', True):
                symbol_stats[symbol]['wins'] += 1
            symbol_stats[symbol]['total_pnl'] += trade.get('net_pnl', 0)
        
        # Calcular win rates por símbolo
        for symbol, stats in symbol_stats.items():
            stats['win_rate'] = stats['wins'] / stats['trades'] if stats['trades'] > 0 else 0
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system': 'QUALIA Advanced Risk Management',
            
            'current_status': {
                'balance': self.current_balance,
                'total_return_pct': total_return * 100,
                'risk_level': risk_level.value,
                'emergency_stop': self.emergency_stop,
                'trading_paused': self.trading_paused,
                'risk_reduction_mode': self.risk_reduction_mode,
                'trades_today': self.trades_today
            },
            
            'risk_metrics': asdict(metrics),
            
            'protection_limits': {
                'max_drawdown': self.max_drawdown_limit,
                'max_position_size': self.max_position_size,
                'ada_max_exposure': self.ada_max_exposure,
                'max_daily_trades': self.max_daily_trades,
                'max_consecutive_losses': self.max_consecutive_losses
            },
            
            'symbol_performance': symbol_stats,
            
            'recent_alerts': [
                {
                    'timestamp': alert.timestamp.isoformat(),
                    'type': alert.alert_type.value,
                    'level': alert.risk_level.value,
                    'message': alert.message,
                    'action': alert.recommended_action
                } for alert in self.risk_alerts[-10:]  # Últimos 10 alertas
            ],
            
            'recommendations': {
                'continue_trading': self.should_continue_trading()[0],
                'reason': self.should_continue_trading()[1],
                'suggested_position_size': f"{self.max_position_size * 0.8:.1%} - {self.max_position_size:.1%}",
                'ada_priority': self.ada_priority_enabled,
                'risk_reduction_active': self.risk_reduction_mode
            }
        }

def main():
    """Demonstração do sistema de gestão de risco"""
    
    print("=" * 70)
    print("QUALIA ADVANCED RISK MANAGEMENT SYSTEM")
    print("=" * 70)
    print("Baseado na validacao empirica:")
    print("- Win Rate: 100% (9/9 trades)")
    print("- Drawdown: 0% (sem perdas)")
    print("- Position Control: 8% maximo")
    print("- ADA-USDT: 77.8% dos trades (prioridade)")
    print("=" * 70)
    
    # Inicializar sistema
    risk_manager = QualiaAdvancedRiskManager()
    risk_manager.initialize_risk_manager(200.0)
    
    # Simular alguns trades
    print(f"\nSimulando trades com gestao de risco...")
    
    # Trade 1: ADA-USDT (sucesso)
    trade1 = {
        'symbol': 'ADA-USDT',
        'is_winner': True,
        'net_pnl': 0.16,
        'balance_after': 200.16
    }
    
    # Validar trade
    can_trade, reason = risk_manager.validate_trade_risk('ADA-USDT', 16.0, 0.75)
    print(f"Trade 1 validacao: {'APROVADO' if can_trade else 'REJEITADO'} - {reason}")
    
    if can_trade:
        risk_manager.register_trade_result(trade1)
    
    # Trade 2: DOGE-USDT (sucesso)
    trade2 = {
        'symbol': 'DOGE-USDT',
        'is_winner': True,
        'net_pnl': 0.16,
        'balance_after': 200.32
    }
    
    can_trade, reason = risk_manager.validate_trade_risk('DOGE-USDT', 16.0, 0.68)
    print(f"Trade 2 validacao: {'APROVADO' if can_trade else 'REJEITADO'} - {reason}")
    
    if can_trade:
        risk_manager.register_trade_result(trade2)
    
    # Gerar relatório
    report = risk_manager.generate_risk_report()
    
    print(f"\n" + "=" * 60)
    print("RELATORIO DE GESTAO DE RISCO")
    print("=" * 60)
    
    status = report['current_status']
    metrics = report['risk_metrics']
    recommendations = report['recommendations']
    
    print(f"\nSTATUS ATUAL:")
    print(f"  Balance: ${status['balance']:.2f}")
    print(f"  Retorno: {status['total_return_pct']:+.2f}%")
    print(f"  Nivel de risco: {status['risk_level'].upper()}")
    print(f"  Emergency stop: {'SIM' if status['emergency_stop'] else 'NAO'}")
    print(f"  Trades hoje: {status['trades_today']}")
    
    print(f"\nMETRICAS DE RISCO:")
    print(f"  Drawdown atual: {metrics['current_drawdown']:.2%}")
    print(f"  Win rate: {metrics['win_rate']:.1%}")
    print(f"  Perdas consecutivas: {metrics['consecutive_losses']}")
    print(f"  Exposicao: {metrics['position_exposure']:.1%}")
    
    print(f"\nRECOMENDACAES:")
    print(f"  Continuar trading: {'SIM' if recommendations['continue_trading'] else 'NAO'}")
    print(f"  Razao: {recommendations['reason']}")
    print(f"  Position size sugerido: {recommendations['suggested_position_size']}")
    print(f"  Prioridade ADA: {'SIM' if recommendations['ada_priority'] else 'NAO'}")
    
    # Performance por símbolo
    if report['symbol_performance']:
        print(f"\nPERFORMANCE POR SIMBOLO:")
        for symbol, stats in report['symbol_performance'].items():
            print(f"  {symbol}: {stats['trades']} trades, "
                  f"Win rate: {stats['win_rate']:.1%}, "
                  f"P&L: ${stats['total_pnl']:+.2f}")
    
    # Salvar relatório
    filename = f"qualia_risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\nRelatorio salvo: {filename}")
    print(f"\n🛡️ Sistema de gestao de risco QUALIA operacional!")

if __name__ == "__main__":
    main()
