@echo off
echo ========================================
echo    QUALIA KILL ALL - CANCELADOR
echo ========================================
echo.
echo ATENCAO: Este script cancela TODAS as execucoes QUALIA ativas!
echo.
echo - Supervisor QUALIA
echo - Sistemas de trading
echo - Processos de monitoramento
echo - Scripts adaptativos
echo.
echo ========================================
echo.

REM Verificar se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Instale Python 3.8+ e tente novamente.
    pause
    exit /b 1
)

REM Verificar se o arquivo killer existe
if not exist "qualia_kill_all.py" (
    echo ERRO: qualia_kill_all.py nao encontrado!
    echo Certifique-se de estar no diretorio correto.
    pause
    exit /b 1
)

echo Iniciando cancelamento de execucoes...
echo.

REM Executar cancelador
python qualia_kill_all.py

echo.
echo ========================================
echo Cancelamento concluido!
echo Verifique os logs para detalhes.
echo ========================================
pause
