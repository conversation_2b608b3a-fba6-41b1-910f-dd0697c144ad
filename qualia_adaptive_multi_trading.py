#!/usr/bin/env python3
"""
QUALIA - Sistema de Trading Multi-Ativos Adaptativo
Combina seleção inteligente de ativos com trading adaptativo

CARACTERÍSTICAS AVANÇADAS:
✅ Seleção dinâmica de ativos em tempo real
✅ Parâmetros adaptativos por categoria
✅ Rotação automática de ativos
✅ Análise de correlação entre ativos
✅ Otimização contínua de portfólio
✅ Sistema de scoring avançado
"""

import asyncio
import ccxt
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import os
from dotenv import load_dotenv
import json

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_adaptive_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdaptiveMultiAssetTrading:
    """Sistema de trading adaptativo com seleção dinâmica de ativos"""
    
    def __init__(self, phase: int = 1):
        # Configurações por fase
        self.phases = {
            1: {'capital': 50, 'max_loss': 5, 'max_position': 15, 'max_assets': 6},
            2: {'capital': 200, 'max_loss': 20, 'max_position': 60, 'max_assets': 10},
            3: {'capital': 500, 'max_loss': 50, 'max_position': 150, 'max_assets': 15}
        }
        
        self.current_phase = phase
        self.phase_config = self.phases[phase]
        
        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')
        
        # Estado
        self.exchange = None
        self.trades = []
        self.running = False
        self.daily_loss = 0.0
        self.trades_today = 0
        
        # Ativos dinâmicos
        self.active_assets = []
        self.asset_scores = {}
        self.asset_performance = {}
        self.last_asset_refresh = None
        
        # Universo expandido de ativos
        self.asset_universe = {
            "tier1": {
                "symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
                "weight": 0.4,  # 40% do portfólio
                "thresholds": {"consciousness": 0.4, "coherence": 0.35, "confidence": 0.4}  # VALIDADO EMPIRICAMENTE
            },
            "tier2": {
                "symbols": ["ADA/USDT", "SOL/USDT", "XMR/USDT", "LINK/USDT", "DOT/USDT", "MATIC/USDT"],
                "weight": 0.35,  # 35% do portfólio
                "thresholds": {"consciousness": 0.45, "coherence": 0.4, "confidence": 0.45}  # Ligeiramente mais rigoroso
            },
            "tier3": {
                "symbols": ["AVAX/USDT", "ATOM/USDT", "FTM/USDT", "NEAR/USDT", "ALGO/USDT", "VET/USDT"],
                "weight": 0.2,   # 20% do portfólio
                "thresholds": {"consciousness": 0.5, "coherence": 0.45, "confidence": 0.5}  # Mais rigoroso mas realista
            },
            "tier4": {
                "symbols": ["UNI/USDT", "AAVE/USDT", "COMP/USDT", "SUSHI/USDT", "CRV/USDT"],
                "weight": 0.05,  # 5% do portfólio
                "thresholds": {"consciousness": 0.55, "coherence": 0.5, "confidence": 0.55}  # Mais conservador
            }
        }
        
        logger.info(f"🚀 QUALIA Adaptive Multi-Asset Trading - Fase {phase}")
        logger.info(f"💰 Capital: ${self.phase_config['capital']}")
        logger.info(f"📊 Máximo de ativos: {self.phase_config['max_assets']}")
    
    async def initialize(self):
        """Inicializa sistema adaptativo"""
        try:
            # Conectar à exchange
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            # Testar conexão
            markets = self.exchange.load_markets()
            balance = self.exchange.fetch_balance()
            
            usdt_free = balance.get('USDT', {}).get('free', 0)
            
            logger.info(f"✅ Conectado à KuCoin - {len(markets)} mercados")
            logger.info(f"💰 USDT disponível: ${usdt_free:.2f}")
            
            # Verificar capital suficiente
            if usdt_free < self.phase_config['capital']:
                logger.error(f"❌ Capital insuficiente: ${usdt_free:.2f} < ${self.phase_config['capital']}")
                return False
            
            # Seleção inicial de ativos
            await self.refresh_asset_selection()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}")
            return False
    
    async def analyze_asset_quality(self, symbol: str) -> Dict:
        """Analisa qualidade de um ativo"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            
            # Métricas básicas
            volume_24h = ticker.get('baseVolume', 0)
            price = ticker.get('last', 0)
            spread = ticker.get('ask', 0) - ticker.get('bid', 0)
            spread_pct = spread / price if price > 0 else 1
            change_24h = ticker.get('percentage', 0) / 100
            
            # Scores individuais
            volume_score = min(volume_24h / 1000, 10) / 10
            spread_score = max(0, 1 - spread_pct * 1000)
            volatility_score = min(abs(change_24h) * 15, 1)  # Volatilidade é oportunidade
            momentum_score = max(0, min(1, change_24h * 10 + 0.5))  # Momentum positivo
            
            # Score composto
            quality_score = (
                volume_score * 0.3 +
                spread_score * 0.3 +
                volatility_score * 0.25 +
                momentum_score * 0.15
            )
            
            return {
                'symbol': symbol,
                'quality_score': quality_score,
                'volume_24h': volume_24h,
                'spread_pct': spread_pct,
                'change_24h': change_24h,
                'momentum_score': momentum_score,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ Erro analisando {symbol}: {e}")
            return None
    
    async def refresh_asset_selection(self):
        """Atualiza seleção de ativos dinamicamente"""
        
        logger.info("🔄 Atualizando seleção de ativos...")
        
        # Analisar todos os ativos do universo
        all_analyses = []
        for tier_data in self.asset_universe.values():
            for symbol in tier_data["symbols"]:
                analysis = await self.analyze_asset_quality(symbol)
                if analysis:
                    # Adicionar informação do tier
                    for tier_name, tier_info in self.asset_universe.items():
                        if symbol in tier_info["symbols"]:
                            analysis['tier'] = tier_name
                            analysis['tier_weight'] = tier_info['weight']
                            break
                    all_analyses.append(analysis)
                
                await asyncio.sleep(0.1)  # Rate limiting
        
        # Selecionar os melhores por tier
        selected_assets = []
        max_assets = self.phase_config['max_assets']
        
        for tier_name, tier_data in self.asset_universe.items():
            # Filtrar análises deste tier
            tier_analyses = [a for a in all_analyses if a.get('tier') == tier_name]
            tier_analyses.sort(key=lambda x: x['quality_score'], reverse=True)
            
            # Calcular quantos ativos selecionar deste tier
            tier_allocation = int(max_assets * tier_data['weight'])
            tier_allocation = max(1, tier_allocation)  # Pelo menos 1 por tier
            
            # Selecionar os melhores do tier
            selected_from_tier = tier_analyses[:tier_allocation]
            selected_assets.extend([a['symbol'] for a in selected_from_tier])
            
            # Salvar scores
            for analysis in selected_from_tier:
                self.asset_scores[analysis['symbol']] = analysis
        
        # Limitar ao máximo permitido
        self.active_assets = selected_assets[:max_assets]
        self.last_asset_refresh = datetime.now()
        
        logger.info(f"✅ Seleção atualizada: {len(self.active_assets)} ativos")
        for i, symbol in enumerate(self.active_assets, 1):
            score = self.asset_scores[symbol]['quality_score']
            tier = self.asset_scores[symbol]['tier']
            logger.info(f"   {i:2d}. {symbol:12s} | Score: {score:.3f} | Tier: {tier}")
    
    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de mercado"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            
            return {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'spread': ticker['ask'] - ticker['bid'],
                'volume': ticker['baseVolume'],
                'change_24h': ticker['percentage'] / 100 if ticker['percentage'] else 0,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ Erro obtendo dados {symbol}: {e}")
            return None
    
    def calculate_adaptive_quantum_signal(self, market_data: Dict) -> Dict:
        """Calcula sinal quântico adaptativo"""
        
        symbol = market_data['symbol']
        
        # Obter configuração do tier
        tier_config = None
        for tier_name, tier_data in self.asset_universe.items():
            if symbol in tier_data["symbols"]:
                tier_config = tier_data
                break
        
        if not tier_config:
            return {'action': 'hold', 'confidence': 0}
        
        # Métricas base
        volatility = abs(market_data['change_24h'])
        spread_pct = market_data['spread'] / market_data['price']
        volume_strength = min(market_data['volume'] / 1000, 1.0)
        
        # Score do ativo (se disponível)
        asset_score = self.asset_scores.get(symbol, {}).get('quality_score', 0.5)
        
        # Coerência quântica adaptativa
        coherence_base = 0.75 - volatility * 8 - spread_pct * 400 + asset_score * 0.1
        coherence = max(0.0, min(1.0, coherence_base))
        
        # Consciência adaptativa
        consciousness_base = 0.70 + volume_strength * 0.25 - volatility * 6 + asset_score * 0.15
        consciousness = max(0.0, min(1.0, consciousness_base))
        
        # Proteção retrocausal
        retrocausal_base = 0.65 - spread_pct * 200 - volatility * 4 + asset_score * 0.2
        retrocausal_protection = max(0.0, min(1.0, retrocausal_base))
        
        # Confiança geral
        confidence = (coherence + consciousness + retrocausal_protection) / 3
        
        # Thresholds do tier
        thresholds = tier_config['thresholds']
        
        # Verificar thresholds
        thresholds_met = {
            'consciousness': consciousness >= thresholds['consciousness'],
            'coherence': coherence >= thresholds['coherence'],
            'confidence': confidence >= thresholds['confidence']
        }
        
        # Gerar sinal
        if all(thresholds_met.values()):
            # Lógica adaptativa por tier
            if tier_config['weight'] >= 0.3:  # Tier 1 e 2 (mais permissivos)
                if market_data['change_24h'] > -0.03:
                    action = 'buy' if consciousness > coherence else 'hold'
                else:
                    action = 'hold'
            else:  # Tier 3 e 4 (mais conservadores)
                if market_data['change_24h'] > -0.015 and retrocausal_protection > 0.7:
                    action = 'buy' if consciousness > coherence * 1.1 else 'hold'
                else:
                    action = 'hold'
        else:
            action = 'hold'
        
        return {
            'action': action,
            'confidence': confidence,
            'coherence': coherence,
            'consciousness': consciousness,
            'retrocausal_protection': retrocausal_protection,
            'asset_score': asset_score,
            'tier_weight': tier_config['weight'],
            'market_data': market_data,
            'thresholds_met': thresholds_met
        }
    
    async def execute_adaptive_trade(self, signal: Dict) -> Optional[Dict]:
        """Executa trade adaptativo"""
        
        if signal['action'] == 'hold':
            return None
        
        try:
            # Verificações de segurança
            if self.daily_loss <= -self.phase_config['max_loss']:
                logger.warning("🚫 Limite diário de perda atingido")
                return None
            
            if self.trades_today >= 25:  # Limite para multi-asset
                logger.warning("🚫 Limite de trades diários atingido")
                return None
            
            # Calcular tamanho da posição adaptativo
            confidence = signal['confidence']
            tier_weight = signal['tier_weight']
            asset_score = signal['asset_score']
            
            # Posição base proporcional ao tier
            base_position = self.phase_config['max_position'] * tier_weight
            position_multiplier = confidence * asset_score * 0.9
            position_size = base_position * position_multiplier
            
            symbol = signal['market_data']['symbol']
            price = signal['market_data']['price']
            
            logger.info(f"🎯 EXECUTANDO TRADE ADAPTATIVO:")
            logger.info(f"   Símbolo: {symbol}")
            logger.info(f"   Tier Weight: {tier_weight:.2f}")
            logger.info(f"   Asset Score: {asset_score:.3f}")
            logger.info(f"   Tamanho: ${position_size:.2f}")
            logger.info(f"   Confiança: {confidence:.3f}")
            
            # Calcular quantidade
            quantity = position_size / price
            
            # EXECUTAR ORDEM REAL
            if signal['action'] == 'buy':
                order = self.exchange.create_market_buy_order(symbol, quantity)
            else:
                logger.warning("⚠️ Venda não implementada")
                return None
            
            # TRATAMENTO ROBUSTO DA RESPOSTA (CORRIGIDO)
            if order:
                order_id = order.get('id', 'N/A')
                status = order.get('status', 'unknown')
                filled_qty = order.get('filled', 0) or 0
                avg_price = order.get('average') or order.get('price') or price

                # Calcular custos de forma segura
                if filled_qty > 0 and avg_price > 0:
                    total_cost = filled_qty * avg_price
                else:
                    total_cost = position_size  # Estimativa baseada na posição

                # Fee seguro
                fee_info = order.get('fee', {})
                if fee_info and fee_info.get('cost'):
                    fee = fee_info['cost']
                else:
                    fee = total_cost * 0.001  # 0.1% estimado
                
                # Registrar trade (CORRIGIDO)
                trade_record = {
                    'id': order_id,
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'action': signal['action'],
                    'quantity': filled_qty,
                    'price': avg_price,
                    'total_cost': total_cost,
                    'fee': fee,
                    'status': status,
                    'confidence': confidence,
                    'tier_weight': tier_weight,
                    'asset_score': asset_score,
                    'quantum_metrics': {
                        'coherence': signal['coherence'],
                        'consciousness': signal['consciousness'],
                        'retrocausal_protection': signal['retrocausal_protection']
                    },
                    'order_raw': order  # Para debug
                }
                
                # Atualizar contadores
                self.daily_loss -= (total_cost + fee)
                self.trades_today += 1
                self.trades.append(trade_record)
                
                logger.info(f"✅ TRADE ADAPTATIVO EXECUTADO:")
                logger.info(f"   ID: {order_id}")
                logger.info(f"   Status: {status}")
                logger.info(f"   Quantidade: {filled_qty:.8f}")
                logger.info(f"   Preço: ${avg_price:.2f}")
                logger.info(f"   Custo total: ${total_cost:.2f}")
                logger.info(f"   Fee: ${fee:.2f}")
                logger.info(f"   Total trades: {len(self.trades)}")

                return trade_record
            else:
                logger.error(f"❌ Ordem retornou None")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro executando trade adaptativo: {e}")
            return None
    
    async def run_adaptive_session(self, duration_minutes: int = 60):
        """Executa sessão de trading adaptativo"""
        
        logger.info(f"🚀 INICIANDO SESSÃO ADAPTATIVA - {duration_minutes} minutos")
        logger.info(f"📊 {len(self.active_assets)} ativos selecionados dinamicamente")
        
        self.running = True
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        cycle = 0
        
        try:
            while datetime.now() < end_time and self.running:
                cycle += 1
                
                logger.info(f"🔄 Ciclo {cycle} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Atualizar seleção de ativos a cada 30 minutos
                if (self.last_asset_refresh is None or 
                    datetime.now() - self.last_asset_refresh > timedelta(minutes=30)):
                    await self.refresh_asset_selection()
                
                # Processar ativos ativos
                for symbol in self.active_assets:
                    if not self.running:
                        break
                    
                    # Obter dados de mercado
                    market_data = await self.get_market_data(symbol)
                    if not market_data:
                        continue
                    
                    # Calcular sinal adaptativo
                    signal = self.calculate_adaptive_quantum_signal(market_data)
                    
                    # Executar trade se sinal válido
                    if signal['action'] != 'hold':
                        trade_result = await self.execute_adaptive_trade(signal)
                        
                        if trade_result:
                            logger.info(f"💎 Trade adaptativo {symbol} executado!")
                            await asyncio.sleep(10)
                
                # Status da sessão
                logger.info(f"🛡️ Status Adaptativo:")
                logger.info(f"   Perda diária: ${self.daily_loss:.2f}")
                logger.info(f"   Trades hoje: {self.trades_today}")
                logger.info(f"   Ativos ativos: {len(self.active_assets)}")
                
                # Aguardar próximo ciclo
                await asyncio.sleep(30)  # 30 segundos para processar mais ativos
                
        except KeyboardInterrupt:
            logger.info("🛑 Sessão adaptativa interrompida")
            self.running = False
        except Exception as e:
            logger.error(f"❌ Erro na sessão adaptativa: {e}")
        
        return self.generate_adaptive_report()
    
    def generate_adaptive_report(self) -> Dict:
        """Gera relatório da sessão adaptativa"""
        
        total_trades = len(self.trades)
        total_invested = sum(t['total_cost'] + t['fee'] for t in self.trades)
        
        # Análise por tier
        tier_analysis = {}
        for tier_name in self.asset_universe.keys():
            tier_trades = []
            for trade in self.trades:
                symbol = trade['symbol']
                if symbol in self.asset_universe[tier_name]['symbols']:
                    tier_trades.append(trade)
            
            if tier_trades:
                tier_analysis[tier_name] = {
                    'trades': len(tier_trades),
                    'total_invested': sum(t['total_cost'] + t['fee'] for t in tier_trades),
                    'avg_confidence': sum(t['confidence'] for t in tier_trades) / len(tier_trades),
                    'avg_asset_score': sum(t['asset_score'] for t in tier_trades) / len(tier_trades)
                }
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'phase': self.current_phase,
            'version': 'ADAPTIVE_MULTI_ASSET',
            'session_summary': {
                'total_trades': total_trades,
                'total_invested': total_invested,
                'daily_loss': self.daily_loss,
                'trades_today': self.trades_today,
                'active_assets': len(self.active_assets),
                'asset_refreshes': 1 if self.last_asset_refresh else 0
            },
            'tier_analysis': tier_analysis,
            'active_assets': self.active_assets,
            'asset_scores': {k: v['quality_score'] for k, v in self.asset_scores.items()},
            'detailed_trades': self.trades
        }
        
        return report

async def main():
    """Função principal do sistema adaptativo"""
    
    print("🌌 QUALIA - SISTEMA ADAPTATIVO MULTI-ATIVOS")
    print("🧠 SELEÇÃO DINÂMICA + TRADING INTELIGENTE")
    print("=" * 60)
    
    print("\n🎯 CARACTERÍSTICAS:")
    print("   ✅ Seleção dinâmica de ativos")
    print("   ✅ Parâmetros adaptativos por tier")
    print("   ✅ Rotação automática de portfólio")
    print("   ✅ Otimização contínua")
    
    # Confirmação
    confirm = input("\nDigite 'AUTORIZO SISTEMA ADAPTATIVO' para continuar: ")
    if confirm != 'AUTORIZO SISTEMA ADAPTATIVO':
        print("❌ Operação cancelada")
        return
    
    # Configuração
    phase = int(input("Selecione a fase (1-3): "))
    if phase not in [1, 2, 3]:
        print("❌ Fase inválida")
        return
    
    duration = int(input("Duração em minutos (recomendado 60-120): "))
    
    # Inicializar sistema
    trading_system = AdaptiveMultiAssetTrading(phase)
    
    try:
        # Inicializar
        if not await trading_system.initialize():
            print("❌ Falha na inicialização")
            return
        
        print(f"\n🚀 Iniciando sistema adaptativo - Fase {phase}")
        print(f"📊 {len(trading_system.active_assets)} ativos selecionados")
        print("🔴 Pressione Ctrl+C para parar")
        
        # Executar sessão
        report = await trading_system.run_adaptive_session(duration)
        
        # Mostrar relatório
        print("\n" + "="*60)
        print("📊 RELATÓRIO DO SISTEMA ADAPTATIVO")
        print("="*60)
        
        summary = report['session_summary']
        tier_analysis = report['tier_analysis']
        
        print(f"Trades executados: {summary['total_trades']}")
        print(f"Capital investido: ${summary['total_invested']:.2f}")
        print(f"Perda/ganho diário: ${summary['daily_loss']:.2f}")
        print(f"Ativos processados: {summary['active_assets']}")
        
        if tier_analysis:
            print("\n📊 ANÁLISE POR TIER:")
            for tier, data in tier_analysis.items():
                print(f"  {tier}: {data['trades']} trades, "
                      f"${data['total_invested']:.2f}, "
                      f"Score médio: {data['avg_asset_score']:.3f}")
        
        # Salvar relatório
        filename = f"qualia_adaptive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Relatório salvo: {filename}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
