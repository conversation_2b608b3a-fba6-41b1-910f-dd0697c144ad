# QUALIA Binance Corrected System Configuration
# Sistema quântico-computacional altamente avançado e auto-evolutivo
# Configuração baseada em validação empírica real

# =============================================================================
# PARÂMETROS DE TRADING - Baseados em validação empírica
# =============================================================================
trading:
  # Parâmetros otimizados (R/R 2:1 FAVORÁVEL)
  profit_target_pct: 0.012      # 1.2% 
  stop_loss_pct: 0.008          # 0.8% 
  position_size_pct: 0.08       # 8% 
  max_position_size_usd: 500    # Máximo por trade
  min_position_size_usd: 21     # Mínimo por trade
  fees_pct: 0.001               # 0.1% - Binance real spot trading

# =============================================================================
# THRESHOLDS QUÂNTICOS - Sistema adaptativo centralizado
# =============================================================================
quantum_thresholds:
  # Thresholds base (modo moderate)
  consciousness: 0.60           # Consciência quântica mínima
  coherence: 0.50               # Coerência do sistema
  confidence: 0.55              # Confiança do sinal
  momentum_min: 0.003           # Momentum mínimo (0.3% - calibrado para crypto)
  volume_surge_min: 1.2         # Surge de volume mínimo

  # Configurações por modo de trading (elimina redundância com qualia_adaptive_threshold_system.py)
  trading_modes:
    conservative:
      consciousness: 0.62
      coherence: 0.52
      confidence: 0.58
      momentum_min: 0.005       # 0.5% - movimentos significativos
      volume_surge_min: 1.25
    moderate:
      consciousness: 0.58
      coherence: 0.48
      confidence: 0.53
      momentum_min: 0.003       # 0.3% - balanceado
      volume_surge_min: 1.1
    aggressive:
      consciousness: 0.53
      coherence: 0.43
      confidence: 0.48
      momentum_min: 0.002       # 0.2% - mais sensível
      volume_surge_min: 0.95

  # Limites para calibração automática
  calibration_limits:
    consciousness:
      min: 0.45
      max: 0.75
    coherence:
      min: 0.40
      max: 0.70
    confidence:
      min: 0.45
      max: 0.70
    volume_surge_min:
      min: 0.8
      max: 2.0
    momentum_min:
      min: 0.001                # Mínimo para baixa volatilidade
      max: 0.008                # Máximo para alta volatilidade

# =============================================================================
# GESTÃO DE RISCO - Execução múltipla otimizada
# =============================================================================
risk_management:
  max_daily_trades: 21          # Máximo de trades por dia
  max_daily_loss_pct: 0.05      # 5% máximo de perda diária
  max_concurrent_trades: 6      # Suporte para execução múltipla
  max_exposure_pct: 0.99        # Máximo 99% do capital exposto
  max_signals_per_cycle: 3      # Máximo 3 sinais por ciclo
  emergency_stop: false         # Parada de emergência

# =============================================================================
# UNIVERSO DE ATIVOS - 4 Tiers baseados em liquidez e performance
# =============================================================================
assets:
  # Tier 1: Máxima liquidez e estabilidade (40% das oportunidades)
  tier1_premium:
    - "BTC/USDT"
    - "ETH/USDT"
    - "BNB/USDT"
  
  # Tier 2: Alta liquidez, boa para trading (35% das oportunidades)
  tier2_major:
    - "ADA/USDT"
    - "SOL/USDT"
    - "XRP/USDT"
    - "LINK/USDT"
    - "AVAX/USDT"
  
  # Tier 3: Boa liquidez, maior potencial (20% das oportunidades)
  tier3_solid:
    - "DOT/USDT"
    - "ATOM/USDT"
    - "LTC/USDT"
  
  # Tier 4: Oportunidades especiais (5% das oportunidades)
  tier4_opportunity:
    - "NEAR/USDT"
    - "ALGO/USDT"
    - "VET/USDT"
    - "SAND/USDT"

# =============================================================================
# CORRELAÇÕES DE ATIVOS - Evitar over-exposure
# =============================================================================
asset_correlations:
  "BTC/USDT": ["ETH/USDT"]      # BTC e ETH são correlacionados
  "ETH/USDT": ["BTC/USDT"]
  "ADA/USDT": ["DOT/USDT"]      # Altcoins similares
  "DOT/USDT": ["ADA/USDT"]
  "SOL/USDT": ["AVAX/USDT"]     # Layer 1 competitors
  "AVAX/USDT": ["SOL/USDT"]

# =============================================================================
# SISTEMA DE SAÚDE DE ATIVOS - Blacklist inteligente
# =============================================================================
asset_health:
  max_consecutive_failures: 3   # Máximo de falhas consecutivas
  blacklist_duration_hours: 2   # Tempo de blacklist em horas
  failure_threshold_per_hour: 5 # Máximo de falhas por hora
  blacklist_file: "qualia_asset_blacklist.json"

# =============================================================================
# AUTO-CALIBRAÇÃO DE PERFORMANCE - Sistema evolutivo
# =============================================================================
performance_calibration:
  calibration_interval_minutes: 30    # Calibrar a cada 30 minutos
  min_trades_for_calibration: 5       # Mínimo de trades para calibrar
  target_win_rate: 0.73              # 62.5% win rate validado empiricamente
  calibration_file: "qualia_performance_calibration.json"
  
  # Regras de calibração
  calibration_rules:
    win_rate_low_threshold: 0.50      # Abaixo de 50% - ajustar agressivamente
    win_rate_high_threshold: 0.75     # Acima de 75% - ajustar conservativamente
    min_trades_for_action: 5
    max_drawdown_threshold: 0.10      # 10% máximo de drawdown
    profit_factor_min: 1.2            # Mínimo profit factor aceitável
    sharpe_ratio_min: 0.5             # Mínimo Sharpe ratio aceitável

# =============================================================================
# CONFIGURAÇÕES DE SISTEMA - Arquivos e logging
# =============================================================================
system:
  # Configurações de logging
  logging:
    level: "INFO"
    format: "%(asctime)s - %(levelname)s - %(message)s"
    file_pattern: "qualia_binance_{timestamp}.log"
    
  # Supressão de logs verbosos
  suppress_external_logs:
    - "ccxt"
    - "urllib3"
    - "requests"
  
  # Configurações de exchange
  exchange:
    name: "binance"
    sandbox: false                    # PRODUÇÃO REAL
    enable_rate_limit: true
    recv_window: 10000               # Timeout
    default_type: "spot"             # Trading spot
    verbose: false                   # Reduzir logs verbosos

# =============================================================================
# MÉTRICAS DE PERFORMANCE - Tracking avançado
# =============================================================================
performance_tracking:
  # Métricas iniciais
  initial_metrics:
    total_signals_generated: 0
    signals_executed: 0
    execution_rate: 0.0
    avg_signal_quality: 0.0
    best_performing_asset: null
  
  # Estatísticas de execução múltipla
  multiple_execution_stats:
    total_opportunities: 0
    executed_signals: 0
    skipped_capital: 0
    skipped_correlation: 0
    skipped_exposure: 0

# =============================================================================
# CONFIGURAÇÕES AVANÇADAS - Métricas quânticas
# =============================================================================
advanced_metrics:
  # Configurações para cálculo de métricas de qualidade
  liquidity_scoring:
    volume_normalization_factor: 1000000  # Normalizar por 1M USDT
    spread_penalty_multiplier: 1000       # Penalizar spreads altos

  stability_scoring:
    volatility_penalty_multiplier: 10     # Penalizar alta volatilidade
    direction_consistency_window: 5       # Janela para consistência direcional

  momentum_quality:
    strength_multiplier: 50               # Multiplicador para força do momentum
    acceleration_weight: 0.3              # Peso da aceleração
    persistence_weight: 0.4               # Peso da persistência
    volume_confirmation_weight: 0.3       # Peso da confirmação por volume

  execution_quality:
    spread_threshold: 0.001               # Threshold para spread aceitável
    volume_threshold: 100000              # Volume mínimo para boa execução
    market_impact_factor: 0.0001          # Fator de impacto no mercado

# =============================================================================
# CÁLCULO DE MÉTRICAS QUÂNTICAS - Parâmetros detalhados
# =============================================================================
quantum_metrics_calculation:
  # Configurações para Consciousness
  consciousness:
    base_value: 0.3                       # Valor base
    volume_component_divisor: 2.0         # Divisor para componente de volume
    volume_component_max: 0.35            # Máximo para componente de volume
    momentum_multiplier: 30               # Multiplicador para momentum
    momentum_component_max: 0.25          # Máximo para componente de momentum
    price_action_multiplier: 10           # Multiplicador para price action
    price_action_component_max: 0.1       # Máximo para componente de price action

  # Configurações para Coherence
  coherence:
    base_value: 0.2                       # Valor base
    momentum_consistency_positive: 1.0    # Valor para momentum consistente
    momentum_consistency_negative: 0.3    # Valor para momentum inconsistente
    volatility_multiplier: 3              # Multiplicador para volatilidade
    volatility_max_penalty: 0.3           # Penalidade máxima por volatilidade
    consistency_weight: 0.4               # Peso da consistência
    low_volatility_bonus: 0.4             # Bônus por baixa volatilidade

  # Configurações para Confidence
  confidence:
    base_value: 0.25                      # Valor base
    spread_penalty_base: 0.1              # Base para penalidade de spread
    spread_penalty_multiplier: 100        # Multiplicador para penalidade de spread
    volume_confidence_divisor: 1.5        # Divisor para confiança de volume
    volume_confidence_max: 0.3            # Máximo para confiança de volume
    momentum_multiplier: 25               # Multiplicador para momentum
    momentum_confidence_max: 0.25         # Máximo para confiança de momentum

# =============================================================================
# THRESHOLDS DE QUALIDADE AVANÇADA
# =============================================================================
advanced_quality_thresholds:
  exceptional_combination:
    consciousness_min: 0.7                # Consciousness mínimo para combinação excepcional
    coherence_min: 0.7                    # Coherence mínimo para combinação excepcional
    confidence_min: 0.7                   # Confidence mínimo para combinação excepcional
    liquidity_score_min: 0.8              # Liquidity score mínimo
    momentum_quality_min: 0.8             # Momentum quality mínimo
    bonus_multiplier: 1.1                 # Multiplicador de bônus

# =============================================================================
# CONDIÇÕES DE MERCADO
# =============================================================================
market_conditions:
  challenging_threshold: 0.4              # Threshold para mercado desafiador
  favorable_threshold: 0.7                # Threshold para mercado favorável

# =============================================================================
# FATORES DE AJUSTE DE CALIBRAÇÃO
# =============================================================================
calibration_adjustment_factors:
  make_aggressive: 0.85                   # Fator para tornar mais agressivo
  make_conservative: 1.15                 # Fator para tornar mais conservativo
  optimize_risk_reward: 1.05              # Fator para otimizar risco/retorno
  optimize_consistency: 1.08              # Fator para otimizar consistência

# =============================================================================
# SCORES DE PERFORMANCE HISTÓRICA
# =============================================================================
historical_performance_scores:
  default_score: 0.50                     # Score padrão para ativos sem histórico
  # Scores específicos por ativo podem ser adicionados aqui
  # "BTC/USDT": 0.75
  # "ETH/USDT": 0.70

# =============================================================================
# CONFIGURAÇÕES DE SISTEMA E TIMEOUTS
# =============================================================================
system_operation:
  timeouts:
    order_fill_wait: 2                    # Aguardar preenchimento de ordem (segundos)
    between_executions: 30                # Entre execuções múltiplas (segundos)
    asset_scan_delay: 0.3                 # Delay entre scan de ativos (segundos)
    between_cycles: 90                    # Entre ciclos de trading (segundos)
    standard_cycle: 60                    # Ciclo padrão (segundos)

# =============================================================================
# CONFIGURAÇÕES DE VALIDAÇÃO - Qualidade de dados
# =============================================================================
data_validation:
  min_ohlcv_length: 10                    # Mínimo de candles OHLCV
  max_spread_pct: 0.05                    # Spread máximo aceitável (5%)
  min_price_variation: true               # Verificar variação de preço
  validate_price_logic: true              # Verificar lógica de preços (high >= low)

  # Configurações específicas de validação
  volume_validation:
    min_24h_volume: 0                     # Volume mínimo 24h
    volume_tail_window: 24                # Janela para cálculo de volume

  price_validation:
    min_price_std: 0                      # Desvio padrão mínimo de preço
    spread_validation_threshold: 0.05     # Threshold para validação de spread

  data_quality:
    min_data_points: 10                   # Mínimo de pontos de dados
    max_null_tolerance: 0                 # Tolerância máxima para valores nulos

# =============================================================================
# CÁLCULO DE MÉTRICAS QUÂNTICAS - Parâmetros de fórmulas
# =============================================================================
quantum_metrics_calculation:
  # Consciousness: Combinação de volume, momentum e price action
  consciousness:
    base_value: 0.3                       # Valor base
    volume_component_divisor: 2.0         # Divisor para componente de volume
    volume_component_max: 0.35            # Máximo para componente de volume
    momentum_multiplier: 30               # Multiplicador para momentum
    momentum_component_max: 0.25          # Máximo para componente de momentum
    price_action_multiplier: 10           # Multiplicador para price action
    price_action_component_max: 0.1       # Máximo para componente de price action

  # Coherence: Consistência direcional e baixa volatilidade
  coherence:
    base_value: 0.2                       # Valor base
    momentum_consistency_positive: 1.0    # Valor para consistência positiva
    momentum_consistency_negative: 0.3    # Valor para consistência negativa
    consistency_weight: 0.4               # Peso da consistência
    volatility_multiplier: 3              # Multiplicador para volatilidade
    volatility_max_penalty: 0.3           # Penalidade máxima por volatilidade
    low_volatility_bonus: 0.4             # Bônus por baixa volatilidade

  # Confidence: Força do sinal e qualidade do setup
  confidence:
    base_value: 0.25                      # Valor base
    spread_penalty_multiplier: 100        # Multiplicador para penalidade de spread
    spread_penalty_base: 0.1              # Base para penalidade de spread
    volume_confidence_divisor: 1.5        # Divisor para confiança de volume
    volume_confidence_max: 0.3            # Máximo para confiança de volume
    momentum_multiplier: 25               # Multiplicador para momentum
    momentum_confidence_max: 0.25         # Máximo para confiança de momentum

  # Momentum multi-timeframe
  momentum:
    timeframe_1m_weight: 0.5              # Peso do momentum 1m
    timeframe_5m_weight: 0.3              # Peso do momentum 5m
    timeframe_15m_weight: 0.2             # Peso do momentum 15m
    min_candles_5m: 12                     # Mínimo de candles para 5m
    min_candles_15m: 21                   # Mínimo de candles para 15m

  # Volume analysis
  volume:
    recent_volume_window: 5               # Janela para volume recente
    avg_volume_window: 20                 # Janela para volume médio
    volume_24h_window: 24                 # Janela para volume 24h

  # Volatilidade
  volatility:
    default_volatility: 0.01              # Volatilidade padrão se não calculável
    min_returns_for_calc: 1               # Mínimo de returns para cálculo

# =============================================================================
# SCORES HISTÓRICOS DE PERFORMANCE - Baseados em dados empíricos
# =============================================================================
historical_performance_scores:
  "ADA/USDT": 0.85      # Melhor performer validado
  "DOT/USDT": 0.80      # Bom histórico
  "BTC/USDT": 0.75      # Estável mas menor volatilidade
  "ETH/USDT": 0.75      # Estável mas menor volatilidade
  "SOL/USDT": 0.70      # Volátil mas oportunidades
  "BNB/USDT": 0.70      # Estável
  "XRP/USDT": 0.65      # Moderado
  "LINK/USDT": 0.65     # Moderado
  "AVAX/USDT": 0.60     # Mais volátil
  "ATOM/USDT": 0.60     # Moderado
  "LTC/USDT": 0.55      # Menor momentum
  "UNI/USDT": 0.55      # Menor momentum
  "NEAR/USDT": 0.50     # Menos dados
  "ALGO/USDT": 0.50     # Menos dados
  "VET/USDT": 0.45      # Menor liquidez
  "SAND/USDT": 0.45     # Menor liquidez
  default_score: 0.50   # Score padrão para ativos não mapeados

# =============================================================================
# CONDIÇÕES DE MERCADO - Thresholds para classificação
# =============================================================================
market_conditions:
  challenging_threshold: 0.4              # Win rate abaixo deste valor = mercado desafiador
  favorable_threshold: 0.7                # Win rate acima deste valor = mercado favorável
  normal_range: [0.4, 0.7]               # Faixa para mercado normal

# =============================================================================
# FATORES DE AJUSTE DE CALIBRAÇÃO - Adjustment factors
# =============================================================================
calibration_adjustment_factors:
  make_aggressive: 0.85                   # Reduzir thresholds em 15%
  make_conservative: 1.15                 # Aumentar thresholds em 15%
  optimize_risk_reward: 1.05              # Ajuste conservativo
  optimize_consistency: 1.08              # Ajuste moderado

# =============================================================================
# CONFIGURAÇÕES DE SISTEMA - Timeouts, delays e operação
# =============================================================================
system_operation:
  # Timeouts e delays
  timeouts:
    order_fill_wait: 2                    # Segundos para aguardar preenchimento
    between_executions: 30                # Segundos entre execuções múltiplas
    between_cycles: 90                    # Segundos entre ciclos (execução múltipla)
    standard_cycle: 60                    # Segundos entre ciclos padrão
    asset_scan_delay: 0.3                 # Segundos entre scan de assets

  # Configurações de inicialização
  initialization:
    balance_usdt_initial: 0.0             # Saldo USDT inicial
    initial_balance: 0.0                  # Balance inicial
    total_pnl_initial: 0.0                # P&L inicial
    win_count_initial: 0                  # Contagem de wins inicial
    loss_count_initial: 0                 # Contagem de losses inicial
    execution_rate_initial: 0.0           # Taxa de execução inicial
    avg_signal_quality_initial: 0.0       # Qualidade média inicial

  # Configurações de assets secundários
  secondary_assets:
    start_index: 1                        # Índice inicial para assets secundários
    end_index: 12                         # Índice final para assets secundários

  # Configurações de logging
  logging:
    separator_length: 70                  # Comprimento do separador "="
    short_separator_length: 60            # Comprimento do separador curto
    failure_reasons_max: 10               # Máximo de razões de falha mantidas

  # Configurações de exchange
  exchange:
    recv_window: 10000                    # Timeout da API
    ohlcv_timeframe: "1m"                 # Timeframe para OHLCV
    ohlcv_limit: 50                       # Limite de candles OHLCV
    non_zero_assets_threshold: 1          # Threshold para assets com saldo > 0

# =============================================================================
# CONFIGURAÇÕES DE QUALIDADE AVANÇADA - Thresholds e multiplicadores
# =============================================================================
advanced_quality_thresholds:
  # Bônus para combinações excepcionais
  exceptional_combination:
    consciousness_min: 0.7                # Consciousness mínimo para bônus
    coherence_min: 0.7                    # Coherence mínimo para bônus
    confidence_min: 0.7                   # Confidence mínimo para bônus
    liquidity_score_min: 0.8              # Liquidity score mínimo para bônus
    momentum_quality_min: 0.8             # Momentum quality mínimo para bônus
    bonus_multiplier: 1.1                 # Multiplicador do bônus

  # Correlação de métricas
  metrics_correlation:
    std_dev_multiplier: 3                 # Multiplicador para desvio padrão
    high_metrics_threshold: 0.7           # Threshold para métricas altas
    high_metrics_bonus: 1.2               # Bônus para métricas consistentemente altas

  # Volume trend
  volume_trend:
    min_series_length: 3                  # Comprimento mínimo da série
    trend_multiplier: 5                   # Multiplicador para normalização da tendência
    default_trend_score: 0.5              # Score padrão se não calculável

  # Position sizing
  position_sizing:
    quality_multiplier_base: 0.6          # Base do multiplicador de qualidade
    quality_multiplier_range: 0.8         # Faixa do multiplicador (0.6 a 1.4)
    quality_threshold: 0.5                # Threshold para qualidade
    fallback_multiplier_base: 0.7         # Base do multiplicador fallback
    fallback_multiplier_range: 0.6        # Faixa do multiplicador fallback (0.7 a 1.3)
    fallback_threshold: 0.6               # Threshold para fallback

# =============================================================================
# CONFIGURAÇÕES DE TRADING AUTÔNOMO - Duração e ciclos
# =============================================================================
autonomous_trading:
  default_duration_hours: 6.0            # Duração padrão em horas
  cycles:
    max_signals_per_cycle: 3              # Máximo de sinais por ciclo
    max_per_direction: 1                  # Máximo por direção (calculado dinamicamente)

  # Configurações de execução múltipla
  multiple_execution:
    max_exposure_pct_display: 42          # Percentual de exposição para display (42%)
    timeout_between_executions: 30        # Timeout entre execuções (segundos)
    execution_rate_baseline: 33.0         # Taxa de aproveitamento baseline (33%)
    diversification_balance_threshold: 0.5 # Threshold para balanceamento BUY/SELL
