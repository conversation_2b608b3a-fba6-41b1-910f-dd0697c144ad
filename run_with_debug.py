#!/usr/bin/env python3
"""
Executa o sistema com debug completo
"""

import asyncio
import sys
import os
import traceback

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def main():
    """Função principal com debug"""
    try:
        from qualia_scalping_system import QualiaScalpingSystem
        
        print("🎯 QUALIA - SISTEMA DE SCALPING INTELIGENTE")
        print("⚡ LUCROS PEQUENOS MAS FREQUENTES")
        print("💰 Target: 0.3-0.8% por trade")
        print("=" * 50)
        
        # Simular entrada do usuário
        phase = 1
        duration = 60
        
        print(f"Fase selecionada: {phase}")
        print(f"Duração: {duration} minutos")
        
        scalping_system = QualiaScalpingSystem(phase)
        
        if not await scalping_system.initialize():
            print("❌ Falha na inicialização")
            return
        
        print("✅ Sistema inicializado com sucesso!")
        
    except Exception as e:
        print(f"❌ ERRO CAPTURADO: {e}")
        print(f"❌ TIPO: {type(e).__name__}")
        print("❌ TRACEBACK COMPLETO:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
