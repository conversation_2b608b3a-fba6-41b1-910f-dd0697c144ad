#!/usr/bin/env python3
"""
Teste simples do sistema de logging de trades
"""

import sys
import os

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_trade_logger():
    """Teste simples do TradeLogger"""
    try:
        print("Importando módulos...")
        from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem, TradeLogger
        
        print("Criando sistema QUALIA...")
        system = QualiaBinanceCorrectedSystem()
        
        print("Verificando atributos do sistema:")
        print(f"  - Tem trade_logger: {hasattr(system, 'trade_logger')}")
        
        if hasattr(system, 'trade_logger'):
            print("  - TradeLogger encontrado!")
            trade_logger = system.trade_logger
            print(f"  - Tipo: {type(trade_logger)}")
            print(f"  - Arquivo de log: {trade_logger.trades_log_file}")
            print(f"  - Histórico: {len(trade_logger.trades_history)} trades")
            
            # Testar resumo
            summary = trade_logger.get_trades_summary()
            print(f"  - Resumo: {summary}")
            
            print("✅ Sistema de logging funcionando!")
        else:
            print("❌ TradeLogger não encontrado!")
            print("Atributos disponíveis:")
            attrs = [attr for attr in dir(system) if not attr.startswith('_')]
            for attr in attrs[:10]:  # Primeiros 10
                print(f"    - {attr}")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trade_logger()
