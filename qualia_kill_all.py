#!/usr/bin/env python3
"""
QUALIA KILL ALL - Cancelador de Execuções Ativas
Cancela todas as execuções ativas do sistema QUALIA de forma segura
"""

import psutil
import time
import logging
import os
import sys
import signal
from datetime import datetime
from pathlib import Path
import json

class QualiaKillAll:
    """Cancelador de todas as execuções QUALIA ativas"""
    
    def __init__(self):
        self.killed_processes = []
        self.failed_kills = []
        self.setup_logging()
        
        # Padrões para identificar processos QUALIA
        self.qualia_patterns = [
            'qualia_supervisor.py',
            'binance_corrected_system.py',
            'qualia_binance_corrected_system.py',
            'qualia_live_trading.py',
            'qualia_adaptive_threshold_system.py',
            'qualia_multi_asset_trading.py',
            'qualia_scalping_system.py',
            'qualia_production_system.py',
            'qualia_monitor.py'
        ]
        
        self.logger.info("🔥 QUALIA KILL ALL INICIALIZADO")
        
    def setup_logging(self):
        """Configura sistema de logging"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configurar logger
        self.logger = logging.getLogger("QualiaKillAll")
        self.logger.setLevel(logging.INFO)
        
        # Handler para arquivo
        log_file = log_dir / f"qualia_kill_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Handler para console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formato
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
    def find_qualia_processes(self):
        """Encontra todos os processos QUALIA ativos"""
        qualia_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    # Verificar se é processo Python
                    if 'python' not in proc.info['name'].lower():
                        continue
                        
                    # Verificar argumentos da linha de comando
                    cmdline = proc.info['cmdline']
                    if not cmdline:
                        continue
                        
                    # Procurar padrões QUALIA nos argumentos
                    cmdline_str = ' '.join(cmdline).lower()
                    
                    for pattern in self.qualia_patterns:
                        if pattern.lower() in cmdline_str:
                            process_info = {
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'cmdline': cmdline,
                                'create_time': proc.info['create_time'],
                                'pattern_matched': pattern,
                                'process': proc
                            }
                            qualia_processes.append(process_info)
                            break
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            self.logger.error(f"❌ Erro ao buscar processos: {e}")
            
        return qualia_processes
        
    def kill_process_gracefully(self, process_info):
        """Termina processo graciosamente"""
        try:
            proc = process_info['process']
            pid = process_info['pid']
            pattern = process_info['pattern_matched']
            
            self.logger.info(f"🎯 Terminando processo: PID {pid} ({pattern})")
            
            # Tentar terminação graciosamente
            proc.terminate()
            
            # Aguardar até 10 segundos
            try:
                proc.wait(timeout=10)
                self.logger.info(f"✅ Processo {pid} terminado graciosamente")
                return True
                
            except psutil.TimeoutExpired:
                # Forçar terminação
                self.logger.warning(f"⚠️ Forçando terminação do processo {pid}")
                proc.kill()
                proc.wait(timeout=5)
                self.logger.info(f"💀 Processo {pid} terminado forçadamente")
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self.logger.warning(f"⚠️ Processo {pid} já não existe ou acesso negado: {e}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao terminar processo {pid}: {e}")
            return False
            
    def kill_all_qualia_processes(self):
        """Mata todos os processos QUALIA encontrados"""
        self.logger.info("🔍 Procurando processos QUALIA ativos...")
        
        # Encontrar processos
        qualia_processes = self.find_qualia_processes()
        
        if not qualia_processes:
            self.logger.info("✅ Nenhum processo QUALIA ativo encontrado")
            return True
            
        self.logger.info(f"🎯 Encontrados {len(qualia_processes)} processos QUALIA ativos")
        
        # Listar processos encontrados
        for proc_info in qualia_processes:
            self.logger.info(f"📋 PID {proc_info['pid']}: {proc_info['pattern_matched']}")
            
        # Confirmar ação
        print("\n" + "="*60)
        print("⚠️  ATENÇÃO: CANCELAMENTO DE EXECUÇÕES ATIVAS")
        print("="*60)
        print(f"Processos QUALIA encontrados: {len(qualia_processes)}")
        for proc_info in qualia_processes:
            print(f"  • PID {proc_info['pid']}: {proc_info['pattern_matched']}")
        print("="*60)
        
        confirm = input("🔥 Confirma o cancelamento de TODOS os processos? (digite 'CANCELAR'): ")
        if confirm != 'CANCELAR':
            self.logger.info("❌ Operação cancelada pelo usuário")
            return False
            
        # Terminar processos
        self.logger.info("🔥 Iniciando cancelamento de processos...")
        
        success_count = 0
        for proc_info in qualia_processes:
            if self.kill_process_gracefully(proc_info):
                self.killed_processes.append(proc_info)
                success_count += 1
            else:
                self.failed_kills.append(proc_info)
                
        # Relatório final
        self.logger.info("=" * 50)
        self.logger.info(f"✅ Processos terminados com sucesso: {success_count}")
        self.logger.info(f"❌ Falhas na terminação: {len(self.failed_kills)}")
        
        if self.failed_kills:
            self.logger.warning("⚠️ Processos que falharam:")
            for proc_info in self.failed_kills:
                self.logger.warning(f"  • PID {proc_info['pid']}: {proc_info['pattern_matched']}")
                
        return len(self.failed_kills) == 0
        
    def cleanup_resources(self):
        """Limpa recursos e arquivos temporários"""
        try:
            self.logger.info("🧹 Limpando recursos...")
            
            # Limpar arquivos de lock se existirem
            lock_patterns = [
                "qualia_*.lock",
                "trading_*.lock",
                "supervisor_*.lock"
            ]
            
            cleanup_count = 0
            for pattern in lock_patterns:
                for lock_file in Path(".").glob(pattern):
                    try:
                        lock_file.unlink()
                        cleanup_count += 1
                        self.logger.info(f"🗑️ Removido: {lock_file}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ Erro ao remover {lock_file}: {e}")
                        
            if cleanup_count > 0:
                self.logger.info(f"✅ {cleanup_count} arquivos de lock removidos")
            else:
                self.logger.info("✅ Nenhum arquivo de lock encontrado")
                
        except Exception as e:
            self.logger.error(f"❌ Erro na limpeza de recursos: {e}")
            
    def generate_kill_report(self):
        """Gera relatório do cancelamento"""
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "total_processes_found": len(self.killed_processes) + len(self.failed_kills),
                "successfully_killed": len(self.killed_processes),
                "failed_kills": len(self.failed_kills),
                "killed_processes": [
                    {
                        "pid": proc['pid'],
                        "pattern": proc['pattern_matched'],
                        "cmdline": proc['cmdline']
                    }
                    for proc in self.killed_processes
                ],
                "failed_processes": [
                    {
                        "pid": proc['pid'],
                        "pattern": proc['pattern_matched'],
                        "cmdline": proc['cmdline']
                    }
                    for proc in self.failed_kills
                ]
            }
            
            report_file = f"qualia_kill_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
                
            self.logger.info(f"📊 Relatório salvo: {report_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao gerar relatório: {e}")
            
    def run(self):
        """Executa o cancelamento completo"""
        try:
            self.logger.info("🔥 INICIANDO CANCELAMENTO DE EXECUÇÕES QUALIA")
            self.logger.info("=" * 60)
            
            # Cancelar processos
            success = self.kill_all_qualia_processes()
            
            # Limpar recursos
            self.cleanup_resources()
            
            # Gerar relatório
            self.generate_kill_report()
            
            # Verificação final
            time.sleep(2)
            remaining_processes = self.find_qualia_processes()
            
            if remaining_processes:
                self.logger.warning(f"⚠️ {len(remaining_processes)} processos ainda ativos após cancelamento")
                for proc in remaining_processes:
                    self.logger.warning(f"  • PID {proc['pid']}: {proc['pattern_matched']}")
            else:
                self.logger.info("✅ Todos os processos QUALIA foram cancelados com sucesso")
                
            self.logger.info("=" * 60)
            self.logger.info("🏁 CANCELAMENTO CONCLUÍDO")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Erro crítico no cancelamento: {e}")
            return False

def main():
    """Função principal"""
    print("🔥 QUALIA KILL ALL - Cancelador de Execuções Ativas")
    print("=" * 60)
    print("⚡ Cancela TODAS as execuções QUALIA ativas")
    print("🛡️ Terminação graciosamente + forçada se necessário")
    print("🧹 Limpeza automática de recursos")
    print("📊 Relatório detalhado de cancelamentos")
    print("=" * 60)
    
    # Criar e executar cancelador
    killer = QualiaKillAll()
    success = killer.run()
    
    if success:
        print("\n✅ TODAS AS EXECUÇÕES CANCELADAS COM SUCESSO!")
    else:
        print("\n⚠️ CANCELAMENTO CONCLUÍDO COM ALGUMAS FALHAS")
        print("Consulte os logs para detalhes")

if __name__ == "__main__":
    main()
