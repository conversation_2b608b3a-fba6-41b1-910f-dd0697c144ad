#!/usr/bin/env python3
"""
QUALIA - Monitor de Trading em Tempo Real
Sistema de monitoramento para acompanhar trading ao vivo

Funcionalidades:
- Dashboard em tempo real
- Alertas de segurança
- Métricas de performance
- Kill switch remoto
- Logs estruturados
"""

import asyncio
import ccxt
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QualiaMonitor:
    """Monitor de trading em tempo real"""
    
    def __init__(self):
        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')
        
        self.exchange = None
        self.monitoring = False
        self.alerts = []
        
    async def initialize(self):
        """Inicializa monitor"""
        try:
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            await self.exchange.load_markets()
            logger.info("✅ Monitor conectado à KuCoin")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no monitor: {e}")
            return False
    
    async def get_account_status(self) -> Dict:
        """Obtém status da conta"""
        try:
            balance = self.exchange.fetch_balance()
            
            # Obter ordens abertas
            open_orders = []
            for symbol in ['BTC/USDT', 'ETH/USDT']:
                try:
                    orders = self.exchange.fetch_open_orders(symbol)
                    open_orders.extend(orders)
                except:
                    pass
            
            # Obter histórico recente
            recent_trades = []
            for symbol in ['BTC/USDT', 'ETH/USDT']:
                try:
                    trades = self.exchange.fetch_my_trades(symbol, limit=10)
                    recent_trades.extend(trades)
                except:
                    pass
            
            return {
                'timestamp': datetime.now(),
                'balance': balance,
                'usdt_free': balance.get('USDT', {}).get('free', 0),
                'usdt_used': balance.get('USDT', {}).get('used', 0),
                'open_orders': len(open_orders),
                'recent_trades': len(recent_trades),
                'last_trades': recent_trades[-5:] if recent_trades else []
            }
            
        except Exception as e:
            logger.error(f"❌ Erro obtendo status: {e}")
            return {}
    
    async def check_safety_alerts(self, status: Dict):
        """Verifica alertas de segurança"""
        alerts = []
        
        # Verificar saldo baixo
        usdt_free = status.get('usdt_free', 0)
        if usdt_free < 10:
            alerts.append({
                'level': 'WARNING',
                'message': f'Saldo USDT baixo: ${usdt_free:.2f}',
                'timestamp': datetime.now()
            })
        
        # Verificar muitas ordens abertas
        open_orders = status.get('open_orders', 0)
        if open_orders > 5:
            alerts.append({
                'level': 'WARNING',
                'message': f'Muitas ordens abertas: {open_orders}',
                'timestamp': datetime.now()
            })
        
        # Verificar trades recentes
        recent_trades = status.get('recent_trades', 0)
        if recent_trades > 20:
            alerts.append({
                'level': 'INFO',
                'message': f'Alta atividade: {recent_trades} trades recentes',
                'timestamp': datetime.now()
            })
        
        return alerts
    
    async def display_dashboard(self, status: Dict, alerts: List[Dict]):
        """Exibe dashboard em tempo real"""
        
        # Limpar tela (Windows/Linux)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🌌 QUALIA - MONITOR DE TRADING EM TEMPO REAL")
        print("=" * 60)
        print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Status da conta
        print("💰 STATUS DA CONTA:")
        print(f"   USDT Livre: ${status.get('usdt_free', 0):.2f}")
        print(f"   USDT Usado: ${status.get('usdt_used', 0):.2f}")
        print(f"   Ordens Abertas: {status.get('open_orders', 0)}")
        print(f"   Trades Recentes: {status.get('recent_trades', 0)}")
        print()
        
        # Últimos trades
        last_trades = status.get('last_trades', [])
        if last_trades:
            print("📈 ÚLTIMOS TRADES:")
            for trade in last_trades:
                timestamp = datetime.fromtimestamp(trade['timestamp'] / 1000)
                print(f"   {timestamp.strftime('%H:%M:%S')} | "
                      f"{trade['symbol']} | "
                      f"{trade['side'].upper()} | "
                      f"{trade['amount']:.6f} @ ${trade['price']:.2f}")
        print()
        
        # Alertas
        if alerts:
            print("🚨 ALERTAS:")
            for alert in alerts[-5:]:  # Últimos 5 alertas
                level_icon = "🔴" if alert['level'] == 'WARNING' else "🟡"
                print(f"   {level_icon} {alert['message']}")
        else:
            print("✅ NENHUM ALERTA")
        print()
        
        # Controles
        print("🎮 CONTROLES:")
        print("   Ctrl+C: Parar monitor")
        print("   'q' + Enter: Sair")
        print("=" * 60)
    
    async def run_monitor(self):
        """Executa monitor em tempo real"""
        
        logger.info("🚀 Iniciando monitor de trading")
        self.monitoring = True
        
        try:
            while self.monitoring:
                # Obter status atual
                status = await self.get_account_status()
                
                if status:
                    # Verificar alertas
                    new_alerts = await self.check_safety_alerts(status)
                    self.alerts.extend(new_alerts)
                    
                    # Manter apenas últimos 50 alertas
                    self.alerts = self.alerts[-50:]
                    
                    # Exibir dashboard
                    await self.display_dashboard(status, self.alerts)
                    
                    # Salvar log estruturado
                    log_entry = {
                        'timestamp': datetime.now().isoformat(),
                        'status': status,
                        'alerts': new_alerts
                    }
                    
                    # Salvar em arquivo de log
                    with open('qualia_monitor.log', 'a') as f:
                        f.write(json.dumps(log_entry, default=str) + '\n')
                
                # Aguardar próxima atualização
                await asyncio.sleep(10)  # Atualizar a cada 10 segundos
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitor interrompido pelo usuário")
        except Exception as e:
            logger.error(f"❌ Erro no monitor: {e}")
        finally:
            self.monitoring = False

async def main():
    """Função principal do monitor"""
    
    monitor = QualiaMonitor()
    
    try:
        # Inicializar
        if not await monitor.initialize():
            print("❌ Falha na inicialização do monitor")
            return
        
        print("🌌 QUALIA Monitor iniciado")
        print("📊 Monitorando trading em tempo real...")
        print("🔴 Pressione Ctrl+C para parar")
        
        # Executar monitor
        await monitor.run_monitor()
        
    except Exception as e:
        logger.error(f"❌ Erro crítico no monitor: {e}")

if __name__ == "__main__":
    asyncio.run(main())
