#!/usr/bin/env python3
"""
Validação completa do sistema de registro de trades QUALIA
Verifica todas as funcionalidades implementadas
"""

import sys
import os
import json
from datetime import datetime

# Adicionar o diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def validar_estrutura_trade_record():
    """Valida se a estrutura TradeRecord está correta"""
    try:
        from qualia.binance_corrected_system import TradeRecord
        
        # Verificar campos obrigatórios
        campos_esperados = [
            'trade_id', 'timestamp', 'symbol', 'direction', 'entry_price',
            'target_price', 'stop_price', 'position_size_usd', 'quantum_metrics',
            'adaptive_system', 'active_thresholds', 'result', 'market_context',
            'filters_applied', 'metadata'
        ]
        
        # Criar instância de teste
        import dataclasses
        campos_definidos = [field.name for field in dataclasses.fields(TradeRecord)]
        
        print("📋 Validação da estrutura TradeRecord:")
        for campo in campos_esperados:
            if campo in campos_definidos:
                print(f"   ✓ {campo}")
            else:
                print(f"   ❌ {campo} - AUSENTE")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro validando TradeRecord: {e}")
        return False

def validar_integracao_sistema():
    """Valida integração com o sistema principal"""
    try:
        from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem
        
        print("\n🔧 Validação da integração:")
        
        # Criar sistema
        system = QualiaBinanceCorrectedSystem()
        
        # Verificar TradeLogger
        if hasattr(system, 'trade_logger'):
            print("   ✓ TradeLogger inicializado")
            
            # Verificar métodos
            metodos_esperados = [
                'create_trade_record', 'update_trade_record', 
                'get_trades_summary', 'save_trades_history'
            ]
            
            for metodo in metodos_esperados:
                if hasattr(system.trade_logger, metodo):
                    print(f"   ✓ Método {metodo}")
                else:
                    print(f"   ❌ Método {metodo} - AUSENTE")
                    return False
        else:
            print("   ❌ TradeLogger não encontrado")
            return False
        
        # Verificar método de resumo no sistema principal
        if hasattr(system, 'display_trades_log_summary'):
            print("   ✓ display_trades_log_summary integrado")
        else:
            print("   ❌ display_trades_log_summary - AUSENTE")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro validando integração: {e}")
        return False

def validar_formato_json():
    """Valida formato do arquivo JSON gerado"""
    try:
        print("\n📄 Validação do formato JSON:")
        
        arquivo_demo = "demo_qualia_trades_log.json"
        if not os.path.exists(arquivo_demo):
            print(f"   ❌ Arquivo {arquivo_demo} não encontrado")
            return False
        
        with open(arquivo_demo, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Verificar estrutura principal
        campos_principais = ['trades', 'last_updated', 'total_trades', 'system_version']
        for campo in campos_principais:
            if campo in data:
                print(f"   ✓ Campo principal: {campo}")
            else:
                print(f"   ❌ Campo principal: {campo} - AUSENTE")
                return False
        
        # Verificar estrutura do trade
        if data['trades']:
            trade = data['trades'][0]
            campos_trade = [
                'trade_id', 'timestamp', 'symbol', 'direction', 'quantum_metrics',
                'adaptive_system', 'active_thresholds', 'result', 'market_context',
                'filters_applied', 'metadata'
            ]
            
            for campo in campos_trade:
                if campo in trade:
                    print(f"   ✓ Campo trade: {campo}")
                else:
                    print(f"   ❌ Campo trade: {campo} - AUSENTE")
                    return False
        
        print(f"   ✓ Arquivo JSON válido ({len(data['trades'])} trades)")
        return True
        
    except Exception as e:
        print(f"❌ Erro validando JSON: {e}")
        return False

def validar_metricas_quanticas():
    """Valida se todas as métricas quânticas estão sendo capturadas"""
    try:
        print("\n🔬 Validação das métricas quânticas:")
        
        arquivo_demo = "demo_qualia_trades_log.json"
        with open(arquivo_demo, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data['trades']:
            print("   ❌ Nenhum trade para validar")
            return False
        
        trade = data['trades'][0]
        quantum_metrics = trade['quantum_metrics']
        
        metricas_esperadas = [
            'consciousness', 'coherence', 'confidence', 'volume_surge',
            'momentum', 'stability_score', 'predictive_score',
            'advanced_quality_score', 'liquidity_score', 'execution_quality',
            'momentum_quality'
        ]
        
        for metrica in metricas_esperadas:
            if metrica in quantum_metrics:
                valor = quantum_metrics[metrica]
                print(f"   ✓ {metrica}: {valor}")
            else:
                print(f"   ❌ {metrica} - AUSENTE")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro validando métricas quânticas: {e}")
        return False

def validar_sistema_adaptativo():
    """Valida captura do sistema adaptativo"""
    try:
        print("\n⚙️ Validação do sistema adaptativo:")
        
        arquivo_demo = "demo_qualia_trades_log.json"
        with open(arquivo_demo, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        trade = data['trades'][0]
        
        # Sistema adaptativo
        adaptive_system = trade['adaptive_system']
        campos_adaptive = ['adaptive_mode', 'approval_rate_current', 'mode_stability_counter', 'cycles_without_signals']
        
        for campo in campos_adaptive:
            if campo in adaptive_system:
                print(f"   ✓ Adaptive: {campo}")
            else:
                print(f"   ❌ Adaptive: {campo} - AUSENTE")
                return False
        
        # Thresholds ativos
        thresholds = trade['active_thresholds']
        campos_thresholds = ['consciousness', 'coherence', 'confidence', 'momentum_min', 'volume_surge_min']
        
        for campo in campos_thresholds:
            if campo in thresholds:
                print(f"   ✓ Threshold: {campo}")
            else:
                print(f"   ❌ Threshold: {campo} - AUSENTE")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro validando sistema adaptativo: {e}")
        return False

def main():
    """Validação completa do sistema"""
    print("🚀 VALIDAÇÃO COMPLETA DO SISTEMA DE REGISTRO DE TRADES QUALIA")
    print("=" * 80)
    
    validacoes = [
        ("Estrutura TradeRecord", validar_estrutura_trade_record),
        ("Integração com Sistema", validar_integracao_sistema),
        ("Formato JSON", validar_formato_json),
        ("Métricas Quânticas", validar_metricas_quanticas),
        ("Sistema Adaptativo", validar_sistema_adaptativo)
    ]
    
    resultados = []
    
    for nome, funcao in validacoes:
        print(f"\n🔍 {nome}:")
        resultado = funcao()
        resultados.append((nome, resultado))
        
        if resultado:
            print(f"✅ {nome}: PASSOU")
        else:
            print(f"❌ {nome}: FALHOU")
    
    # Resumo final
    print("\n" + "=" * 80)
    print("📊 RESUMO DA VALIDAÇÃO")
    print("=" * 80)
    
    passou = sum(1 for _, resultado in resultados if resultado)
    total = len(resultados)
    
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"   {nome}: {status}")
    
    print(f"\n🎯 RESULTADO FINAL: {passou}/{total} validações passaram")
    
    if passou == total:
        print("\n🎉 SISTEMA COMPLETAMENTE VALIDADO!")
        print("✅ Todas as funcionalidades estão implementadas corretamente")
        print("✅ Sistema pronto para uso em produção")
        
        print("\n📋 FUNCIONALIDADES VALIDADAS:")
        print("   ✓ Estrutura de dados completa")
        print("   ✓ Integração automática com sistema de trading")
        print("   ✓ Captura de todas as métricas quânticas")
        print("   ✓ Sistema adaptativo e thresholds")
        print("   ✓ Persistência em JSON estruturado")
        print("   ✓ Compatibilidade com especificação original")
        
    else:
        print(f"\n⚠️ {total - passou} validações falharam")
        print("❌ Sistema precisa de correções antes do uso")
    
    return passou == total

if __name__ == "__main__":
    sucesso = main()
    exit(0 if sucesso else 1)
