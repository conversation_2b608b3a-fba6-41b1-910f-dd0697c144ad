#!/usr/bin/env python3
"""
Exemplo de uso do Sistema QUALIA com configuração YAML
Sistema quântico-computacional altamente avançado e auto-evolutivo
"""

import asyncio
import logging
import os
from src.qualia.binance_corrected_system import QualiaBinanceCorrectedSystem

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def main():
    """Exemplo de execução do sistema QUALIA com configuração YAML"""
    
    logger.info("=" * 70)
    logger.info("🌌 INICIANDO SISTEMA QUALIA COM CONFIGURAÇÃO YAML")
    logger.info("=" * 70)
    
    try:
        # Verificar se arquivo de configuração existe
        config_file = 'qualia_config.yaml'
        if not os.path.exists(config_file):
            logger.error(f"❌ ERRO: Arquivo de configuração não encontrado: {config_file}")
            logger.info("   Certifique-se de que o arquivo qualia_config.yaml existe no diretório atual.")
            return

        # Inicializar sistema com configuração YAML rigorosa
        # Sistema falhará se configuração for inválida - isso é intencional!
        logger.info(f"📋 Carregando configuração de {config_file}...")
        qualia_system = QualiaBinanceCorrectedSystem(config_file)

        # Conectar à Binance
        logger.info("🔗 Conectando à Binance...")
        connected = await qualia_system.initialize_binance_connection()

        if not connected:
            logger.error("❌ Falha na conexão com Binance")
            return
        
        logger.info("✅ Conexão estabelecida com sucesso!")
        
        # Exibir configurações carregadas
        logger.info("📋 CONFIGURAÇÕES CARREGADAS:")
        logger.info(f"   • Profit Target: {qualia_system.trading_params['profit_target_pct']:.1%}")
        logger.info(f"   • Stop Loss: {qualia_system.trading_params['stop_loss_pct']:.1%}")
        logger.info(f"   • Position Size: {qualia_system.trading_params['position_size_pct']:.1%}")
        logger.info(f"   • Consciousness Threshold: {qualia_system.quantum_thresholds['consciousness']:.2f}")
        logger.info(f"   • Coherence Threshold: {qualia_system.quantum_thresholds['coherence']:.2f}")
        logger.info(f"   • Confidence Threshold: {qualia_system.quantum_thresholds['confidence']:.2f}")
        logger.info(f"   • Max Daily Trades: {qualia_system.risk_limits['max_daily_trades']}")
        logger.info(f"   • Max Exposure: {qualia_system.risk_limits['max_exposure_pct']:.1%}")
        logger.info(f"   • Asset Tiers: {len(qualia_system.all_assets)} ativos em 4 tiers")
        
        # Exemplo de execução de análise
        logger.info("🔍 Executando análise de mercado...")
        
        # Analisar alguns ativos principais
        for symbol in qualia_system.asset_tiers['tier1_premium'][:2]:  # Apenas 2 para exemplo
            logger.info(f"📊 Analisando {symbol}...")
            
            # Obter dados de mercado
            market_data = await qualia_system.get_market_data(symbol)
            
            if market_data:
                # Calcular métricas quânticas
                quantum_metrics = qualia_system.calculate_enhanced_quantum_metrics(market_data)
                
                if quantum_metrics:
                    logger.info(f"   🧠 Consciousness: {quantum_metrics['consciousness']:.3f}")
                    logger.info(f"   🔗 Coherence: {quantum_metrics['coherence']:.3f}")
                    logger.info(f"   💪 Confidence: {quantum_metrics['confidence']:.3f}")
                    logger.info(f"   ⚡ Momentum: {quantum_metrics['momentum']:.4f}")
                    logger.info(f"   📈 Volume Surge: {quantum_metrics['volume_surge']:.2f}")
                    
                    # Verificar se passa nos thresholds
                    signal = qualia_system.evaluate_trading_signal(symbol, market_data, quantum_metrics)
                    
                    if signal:
                        logger.info(f"   ✅ SINAL DETECTADO: {signal.direction.upper()} {signal.symbol}")
                        logger.info(f"      Entry: ${signal.entry_price:.4f}")
                        logger.info(f"      Target: ${signal.target_price:.4f}")
                        logger.info(f"      Stop: ${signal.stop_price:.4f}")
                        logger.info(f"      Size: ${signal.position_size_usd:.2f}")
                    else:
                        logger.info(f"   ⏸️ Nenhum sinal detectado (thresholds não atingidos)")
                else:
                    logger.warning(f"   ⚠️ Erro calculando métricas quânticas para {symbol}")
            else:
                logger.warning(f"   ⚠️ Erro obtendo dados de mercado para {symbol}")
        
        logger.info("=" * 70)
        logger.info("✅ EXEMPLO CONCLUÍDO COM SUCESSO")
        logger.info("=" * 70)
        logger.info("💡 PRÓXIMOS PASSOS:")
        logger.info("   1. Ajuste os parâmetros em qualia_config.yaml conforme necessário")
        logger.info("   2. Execute o sistema completo para trading real")
        logger.info("   3. Monitore as métricas de performance e calibração automática")
        logger.info("   4. IMPORTANTE: Sistema usa APENAS configuração do YAML - sem fallbacks!")
        logger.info("=" * 70)
        
    except Exception as e:
        logger.error(f"❌ Erro durante execução: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Executar exemplo
    asyncio.run(main())
