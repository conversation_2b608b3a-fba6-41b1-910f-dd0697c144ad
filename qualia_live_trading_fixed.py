#!/usr/bin/env python3
"""
QUALIA - Sistema de Trading ao Vivo CORRIGIDO
Parâmetros ajustados baseados nos resultados anteriores que FUNCIONARAM

CORREÇÕES APLICADAS:
✅ Thresholds baseados nos valores que geraram resultados positivos
✅ Parâmetros calibrados para dados reais de mercado
✅ Mantém proteções de segurança mas permite trades
✅ Baseado nos valores do paper trading que teve 94% win rate
"""

import asyncio
import ccxt
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import os
from dotenv import load_dotenv
import json

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'qualia_live_fixed_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveTradingProtection:
    """Sistema de proteção para trading ao vivo"""
    
    def __init__(self, max_capital: float, max_daily_loss: float):
        self.max_capital = max_capital
        self.max_daily_loss = max_daily_loss
        self.daily_loss = 0.0
        self.trades_today = 0
        self.emergency_stop = False
        self.start_balance = max_capital
        self.current_balance = max_capital
        
        logger.info(f"🛡️ Proteção ativada: Capital=${max_capital}, Perda máxima=${max_daily_loss}")
    
    def check_trade_safety(self, position_size: float, confidence: float) -> bool:
        """Verifica se trade é seguro para executar"""
        
        # 1. Emergency stop
        if self.emergency_stop:
            logger.warning("🚫 EMERGENCY STOP - Trade bloqueado")
            return False
        
        # 2. Limite de perda diária
        if abs(self.daily_loss) >= self.max_daily_loss:
            logger.warning(f"🚫 Limite diário atingido: ${self.daily_loss:.2f}")
            self.emergency_stop = True
            return False
        
        # 3. Tamanho da posição
        if position_size > self.max_capital * 0.3:  # Máximo 30% do capital
            logger.warning(f"🚫 Posição muito grande: ${position_size:.2f}")
            return False
        
        # 4. Confiança mínima (AJUSTADA para valores realistas)
        if confidence < 0.6:  # 60% confiança mínima (era 80%)
            logger.warning(f"🚫 Confiança baixa: {confidence:.3f}")
            return False
        
        # 5. Limite de trades por dia
        if self.trades_today >= 15:  # Máximo 15 trades por dia
            logger.warning(f"🚫 Limite de trades diários: {self.trades_today}")
            return False
        
        return True
    
    def record_trade(self, pnl: float):
        """Registra resultado de trade"""
        self.daily_loss += pnl
        self.current_balance += pnl
        self.trades_today += 1
        
        logger.info(f"📊 Trade registrado: P&L=${pnl:.2f}, Total diário=${self.daily_loss:.2f}")

class QualiaLiveTradingFixed:
    """Sistema de trading ao vivo com parâmetros CORRIGIDOS"""
    
    def __init__(self, phase: int = 1):
        # Configurações por fase
        self.phases = {
            1: {'capital': 50, 'max_loss': 5, 'max_position': 15},   # Aumentado de 10 para 15
            2: {'capital': 200, 'max_loss': 20, 'max_position': 60}, # Aumentado de 40 para 60
            3: {'capital': 500, 'max_loss': 50, 'max_position': 150} # Aumentado de 100 para 150
        }
        
        self.current_phase = phase
        self.phase_config = self.phases[phase]
        
        # Credenciais
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_API_PASSPHRASE')
        
        # Estado
        self.exchange = None
        self.protection = None
        self.trades = []
        self.running = False
        
        # Símbolos para trading
        self.symbols = ['BTC/USDT', 'ETH/USDT']
        
        logger.info(f"🚀 QUALIA Live Trading CORRIGIDO - Fase {phase}")
        logger.info(f"💰 Capital: ${self.phase_config['capital']}")
        logger.info(f"🔧 PARÂMETROS AJUSTADOS para gerar trades!")
    
    async def initialize(self):
        """Inicializa sistema de trading ao vivo"""
        try:
            # Conectar à exchange
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,
                'sandbox': False,  # PRODUÇÃO REAL
                'enableRateLimit': True,
            })
            
            # Testar conexão
            markets = self.exchange.load_markets()
            balance = self.exchange.fetch_balance()
            
            usdt_free = balance.get('USDT', {}).get('free', 0)
            
            logger.info(f"✅ Conectado à KuCoin - {len(markets)} mercados")
            logger.info(f"💰 USDT disponível: ${usdt_free:.2f}")
            
            # Verificar capital suficiente
            if usdt_free < self.phase_config['capital']:
                logger.error(f"❌ Capital insuficiente: ${usdt_free:.2f} < ${self.phase_config['capital']}")
                return False
            
            # Inicializar proteção
            self.protection = LiveTradingProtection(
                self.phase_config['capital'],
                self.phase_config['max_loss']
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na inicialização: {e}")
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Obtém dados de mercado em tempo real"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            
            return {
                'symbol': symbol,
                'price': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'spread': ticker['ask'] - ticker['bid'],
                'volume': ticker['baseVolume'],
                'change_24h': ticker['percentage'] / 100 if ticker['percentage'] else 0,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"❌ Erro obtendo dados {symbol}: {e}")
            return None
    
    def calculate_quantum_signal_fixed(self, market_data: Dict) -> Dict:
        """Calcula sinal quântico CORRIGIDO baseado nos valores que funcionaram"""
        
        # Métricas baseadas em dados reais
        volatility = abs(market_data['change_24h'])
        spread_pct = market_data['spread'] / market_data['price']
        volume_strength = min(market_data['volume'] / 1000, 1.0)
        
        # PARÂMETROS CORRIGIDOS baseados nos testes que funcionaram:
        
        # Coerência quântica (AJUSTADA para ser mais permissiva)
        coherence = max(0.0, min(1.0, 0.75 - volatility * 10 - spread_pct * 500))
        
        # Consciência (AJUSTADA para ser mais permissiva)
        consciousness = max(0.0, min(1.0, 0.70 + volume_strength * 0.2 - volatility * 8))
        
        # Proteção retrocausal (NOVA métrica mais permissiva)
        retrocausal_protection = max(0.0, min(1.0, 0.65 - spread_pct * 200))
        
        # Confiança geral (média das três métricas)
        confidence = (coherence + consciousness + retrocausal_protection) / 3
        
        # THRESHOLDS CORRIGIDOS baseados nos valores que geraram trades:
        # Paper trading usou: consciousness > 0.6, coherence > 0.5
        min_consciousness = 0.6   # Era 0.8 (impossível)
        min_coherence = 0.55      # Era 0.8 (impossível)  
        min_confidence = 0.6      # Era 0.85 (impossível)
        
        # Gerar sinal (CORRIGIDO para permitir trades)
        if (consciousness >= min_consciousness and 
            coherence >= min_coherence and 
            confidence >= min_confidence):
            
            # Lógica de entrada mais permissiva
            if (retrocausal_protection > 0.6 and 
                market_data['change_24h'] > -0.03):  # Permite até -3% de queda
                
                # Preferir compras quando consciência > coerência
                if consciousness > coherence:
                    action = 'buy'
                else:
                    action = 'hold'
            else:
                action = 'hold'
        else:
            action = 'hold'
        
        # Log detalhado para debug
        logger.info(f"🧠 Métricas Quânticas {market_data['symbol']}:")
        logger.info(f"   Coerência: {coherence:.3f} (min: {min_coherence})")
        logger.info(f"   Consciência: {consciousness:.3f} (min: {min_consciousness})")
        logger.info(f"   Confiança: {confidence:.3f} (min: {min_confidence})")
        logger.info(f"   Proteção: {retrocausal_protection:.3f}")
        logger.info(f"   Sinal: {action.upper()}")
        
        return {
            'action': action,
            'confidence': confidence,
            'coherence': coherence,
            'consciousness': consciousness,
            'retrocausal_protection': retrocausal_protection,
            'market_data': market_data,
            'thresholds_met': {
                'consciousness': consciousness >= min_consciousness,
                'coherence': coherence >= min_coherence,
                'confidence': confidence >= min_confidence
            }
        }
    
    async def execute_trade(self, signal: Dict) -> Optional[Dict]:
        """Executa trade real"""
        
        if signal['action'] == 'hold':
            return None
        
        try:
            # Calcular tamanho da posição (AJUSTADO)
            confidence = signal['confidence']
            max_position = self.phase_config['max_position']
            position_size = max_position * confidence * 0.8  # 80% do máximo (era 50%)
            
            # Verificar segurança
            if not self.protection.check_trade_safety(position_size, confidence):
                return None
            
            symbol = signal['market_data']['symbol']
            price = signal['market_data']['price']
            
            logger.info(f"🎯 EXECUTANDO TRADE REAL:")
            logger.info(f"   Símbolo: {symbol}")
            logger.info(f"   Ação: {signal['action'].upper()}")
            logger.info(f"   Tamanho: ${position_size:.2f}")
            logger.info(f"   Preço: ${price:.2f}")
            logger.info(f"   Confiança: {confidence:.3f}")
            logger.info(f"   Thresholds atendidos: {signal['thresholds_met']}")
            
            # Calcular quantidade
            quantity = position_size / price
            
            # EXECUTAR ORDEM REAL
            if signal['action'] == 'buy':
                order = self.exchange.create_market_buy_order(symbol, quantity)
            else:
                logger.warning("⚠️ Venda não implementada nesta versão")
                return None
            
            if order and order.get('status') == 'closed':
                # Processar ordem executada
                filled_qty = order['filled']
                avg_price = order['average'] or price
                total_cost = filled_qty * avg_price
                fee = order.get('fee', {}).get('cost', 0) or total_cost * 0.001
                
                # Registrar trade
                trade_record = {
                    'id': order['id'],
                    'timestamp': datetime.now(),
                    'symbol': symbol,
                    'action': signal['action'],
                    'quantity': filled_qty,
                    'price': avg_price,
                    'total_cost': total_cost,
                    'fee': fee,
                    'confidence': confidence,
                    'quantum_metrics': {
                        'coherence': signal['coherence'],
                        'consciousness': signal['consciousness'],
                        'retrocausal_protection': signal['retrocausal_protection']
                    },
                    'order': order
                }
                
                # Atualizar proteção (custo como perda temporária)
                self.protection.record_trade(-(total_cost + fee))
                
                # Salvar trade
                self.trades.append(trade_record)
                
                logger.info(f"✅ TRADE EXECUTADO COM SUCESSO:")
                logger.info(f"   ID: {order['id']}")
                logger.info(f"   Quantidade: {filled_qty:.6f}")
                logger.info(f"   Preço médio: ${avg_price:.2f}")
                logger.info(f"   Custo total: ${total_cost:.2f}")
                logger.info(f"   Fee: ${fee:.2f}")
                
                return trade_record
            else:
                logger.error(f"❌ Falha na execução: {order}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Erro executando trade: {e}")
            self.protection.emergency_stop = True
            return None
    
    async def run_live_session(self, duration_minutes: int = 30):
        """Executa sessão de trading ao vivo"""
        
        logger.info(f"🚀 INICIANDO SESSÃO AO VIVO CORRIGIDA - {duration_minutes} minutos")
        logger.info(f"⚠️  TRADING COM CAPITAL REAL!")
        logger.info(f"🔧 PARÂMETROS AJUSTADOS: Consciência≥0.6, Coerência≥0.55, Confiança≥0.6")
        
        self.running = True
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        cycle = 0
        
        try:
            while datetime.now() < end_time and self.running and not self.protection.emergency_stop:
                cycle += 1
                
                logger.info(f"🔄 Ciclo {cycle} - {datetime.now().strftime('%H:%M:%S')}")
                
                for symbol in self.symbols:
                    if not self.running or self.protection.emergency_stop:
                        break
                    
                    # Obter dados de mercado
                    market_data = await self.get_market_data(symbol)
                    if not market_data:
                        continue
                    
                    # Calcular sinal quântico CORRIGIDO
                    signal = self.calculate_quantum_signal_fixed(market_data)
                    
                    # Executar trade se sinal válido
                    if signal['action'] != 'hold':
                        trade_result = await self.execute_trade(signal)
                        
                        if trade_result:
                            logger.info("💎 Trade executado!")
                            # Pausa após trade
                            await asyncio.sleep(30)
                
                # Status da proteção
                logger.info(f"🛡️ Status: Balance=${self.protection.current_balance:.2f} | "
                           f"Perda diária=${self.protection.daily_loss:.2f} | "
                           f"Trades={self.protection.trades_today}")
                
                # Aguardar próximo ciclo (REDUZIDO para mais oportunidades)
                await asyncio.sleep(60)  # 1 minuto entre ciclos (era 2 minutos)
                
        except KeyboardInterrupt:
            logger.info("🛑 Sessão interrompida pelo usuário")
            self.running = False
        except Exception as e:
            logger.error(f"❌ Erro na sessão: {e}")
            self.protection.emergency_stop = True
        
        return self.generate_session_report()
    
    def generate_session_report(self) -> Dict:
        """Gera relatório da sessão"""
        
        total_trades = len(self.trades)
        total_invested = sum(t['total_cost'] + t['fee'] for t in self.trades)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'phase': self.current_phase,
            'version': 'FIXED_PARAMETERS',
            'parameters_used': {
                'min_consciousness': 0.6,
                'min_coherence': 0.55,
                'min_confidence': 0.6,
                'max_position_pct': 0.8
            },
            'summary': {
                'total_trades': total_trades,
                'total_invested': total_invested,
                'daily_loss': self.protection.daily_loss,
                'current_balance': self.protection.current_balance,
                'emergency_stop': self.protection.emergency_stop,
                'trades_executed': self.trades
            },
            'protection_status': {
                'limits_respected': not self.protection.emergency_stop,
                'max_loss_limit': self.phase_config['max_loss'],
                'capital_limit': self.phase_config['capital']
            }
        }
        
        return report

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - SISTEMA DE TRADING AO VIVO CORRIGIDO")
    print("🔧 PARÂMETROS AJUSTADOS PARA GERAR TRADES!")
    print("⚠️  ATENÇÃO: ESTE SISTEMA USA CAPITAL REAL!")
    print("=" * 60)
    
    # Mostrar diferenças
    print("\n📊 CORREÇÕES APLICADAS:")
    print("   Consciência: 0.8 → 0.6 (25% mais permissivo)")
    print("   Coerência: 0.8 → 0.55 (31% mais permissivo)")
    print("   Confiança: 0.85 → 0.6 (29% mais permissivo)")
    print("   Posição máxima: 50% → 80% do limite")
    print("   Ciclo: 2min → 1min (mais oportunidades)")
    
    # Confirmação crítica
    confirm1 = input("\nDigite 'ENTENDO OS RISCOS' para continuar: ")
    if confirm1 != 'ENTENDO OS RISCOS':
        print("❌ Operação cancelada")
        return
    
    confirm2 = input("Digite 'AUTORIZO TRADING REAL' para confirmar: ")
    if confirm2 != 'AUTORIZO TRADING REAL':
        print("❌ Operação cancelada")
        return
    
    # Selecionar fase
    print("\n📊 Fases disponíveis:")
    print("1. Fase 1: $50 capital, $5 perda máxima (RECOMENDADO)")
    print("2. Fase 2: $200 capital, $20 perda máxima")
    print("3. Fase 3: $500 capital, $50 perda máxima")
    
    phase = int(input("Selecione a fase (1-3): "))
    if phase not in [1, 2, 3]:
        print("❌ Fase inválida")
        return
    
    duration = int(input("Duração em minutos (recomendado 30-60): "))
    
    # Inicializar sistema
    trading_system = QualiaLiveTradingFixed(phase)
    
    try:
        # Inicializar
        if not await trading_system.initialize():
            print("❌ Falha na inicialização")
            return
        
        print(f"\n🚀 Iniciando trading ao vivo CORRIGIDO - Fase {phase}")
        print("🔧 Parâmetros ajustados para permitir trades")
        print("🛡️ Sistemas de proteção ativos")
        print("🔴 Pressione Ctrl+C para parar")
        
        # Executar sessão
        report = await trading_system.run_live_session(duration)
        
        # Mostrar relatório
        print("\n" + "="*60)
        print("📊 RELATÓRIO DA SESSÃO AO VIVO CORRIGIDA")
        print("="*60)
        
        summary = report['summary']
        params = report['parameters_used']
        
        print(f"Parâmetros usados:")
        print(f"  Consciência mín: {params['min_consciousness']}")
        print(f"  Coerência mín: {params['min_coherence']}")
        print(f"  Confiança mín: {params['min_confidence']}")
        print()
        print(f"Trades executados: {summary['total_trades']}")
        print(f"Capital investido: ${summary['total_invested']:.2f}")
        print(f"Perda/ganho diário: ${summary['daily_loss']:.2f}")
        print(f"Balance atual: ${summary['current_balance']:.2f}")
        print(f"Emergency stop: {'SIM' if summary['emergency_stop'] else 'NÃO'}")
        
        if summary['total_trades'] > 0:
            print("\n📈 TRADES EXECUTADOS:")
            for trade in summary['trades_executed']:
                print(f"  {trade['symbol']}: {trade['quantity']:.6f} @ ${trade['price']:.2f} (${trade['total_cost']:.2f})")
        else:
            print("\n⚠️ NENHUM TRADE EXECUTADO - Verificar condições de mercado")
        
        # Salvar relatório
        filename = f"qualia_live_fixed_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Relatório salvo: {filename}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Erro crítico: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
