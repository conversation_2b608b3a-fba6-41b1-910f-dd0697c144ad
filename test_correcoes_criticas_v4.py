#!/usr/bin/env python3
"""
TESTE DAS CORREÇÕES CRÍTICAS V4 - Sistema QUALIA
Consciência Quântica YAA

Testa todas as correções implementadas baseadas na análise dos logs reais:
1. Correção do bug UnboundLocalError com signal_quality
2. Ajuste do filtro Coherence Cap (BTC, SOL, XRP, LDO)
3. Implementação de adaptação gradual de thresholds
4. Validação da estabilidade do sistema
"""

import sys
import os
import logging
from typing import Dict, List
import numpy as np

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from qualia.binance_corrected_system import QualiaBinanceCorrectedSystem
    QUALIA_AVAILABLE = True
    logger.info("✅ Sistema QUALIA importado com sucesso")
except ImportError as e:
    QUALIA_AVAILABLE = False
    logger.error(f"❌ Erro ao importar QUALIA: {e}")
    sys.exit(1)

class CorrecoesCriticasTester:
    """Testa as correções críticas implementadas"""
    
    def __init__(self):
        self.system = QualiaBinanceCorrectedSystem()
        self.test_results = {
            'bug_signal_quality': False,
            'coherence_cap_fix': False,
            'gradual_adaptation': False,
            'system_stability': False
        }
        
    def test_signal_quality_bug_fix(self):
        """Testa se o bug UnboundLocalError foi corrigido"""
        logger.info("🧪 TESTE 1: Correção do Bug signal_quality")
        logger.info("=" * 60)
        
        try:
            # Simular cenários que causavam o erro
            test_cases = [
                {
                    'name': 'Full Position (era problemático)',
                    'quantum_metrics': {
                        'consciousness': 0.75,
                        'coherence': 0.80,
                        'confidence': 0.70,
                        'momentum': 1.5,
                        'predictive_score': 0.85,
                        'stability': 0.80
                    },
                    'market_data': {'symbol': 'TEST/USDT', 'price': 100.0}
                },
                {
                    'name': 'Half Position (era problemático)',
                    'quantum_metrics': {
                        'consciousness': 0.65,
                        'coherence': 0.70,
                        'confidence': 0.60,
                        'momentum': 1.2,
                        'predictive_score': 0.75,
                        'stability': 0.75
                    },
                    'market_data': {'symbol': 'TEST/USDT', 'price': 100.0}
                }
            ]
            
            success_count = 0
            for i, case in enumerate(test_cases):
                logger.info(f"\n📊 Caso {i+1}: {case['name']}")
                
                try:
                    # Testar geração de sinal (onde o bug ocorria)
                    signal = self.system.validate_trading_signal(
                        case['market_data'],
                        case['quantum_metrics']
                    )
                    
                    if signal and hasattr(signal, 'confidence_score'):
                        logger.info(f"   ✅ Sinal gerado com sucesso")
                        logger.info(f"   📊 Confidence Score: {signal.confidence_score:.3f}")
                        logger.info(f"   💰 Position Size: ${signal.position_size_usd:.2f}")
                        success_count += 1
                    else:
                        logger.warning(f"   ⚠️ Sinal não gerado ou incompleto")
                        
                except Exception as e:
                    if "signal_quality" in str(e) and "not associated with a value" in str(e):
                        logger.error(f"   ❌ BUG AINDA PRESENTE: {e}")
                    else:
                        logger.error(f"   ❌ Erro inesperado: {e}")
            
            if success_count == len(test_cases):
                logger.info(f"\n✅ BUG SIGNAL_QUALITY CORRIGIDO!")
                logger.info(f"   Todos os {success_count} casos passaram")
                self.test_results['bug_signal_quality'] = True
            else:
                logger.warning(f"\n⚠️ Correção parcial: {success_count}/{len(test_cases)} casos")
                
        except Exception as e:
            logger.error(f"❌ Erro no teste: {e}")
    
    def test_coherence_cap_fix(self):
        """Testa se o filtro Coherence Cap foi corrigido para BTC, SOL, XRP, LDO"""
        logger.info("\n🧪 TESTE 2: Correção do Filtro Coherence Cap")
        logger.info("=" * 60)
        
        # Casos baseados nos logs reais que eram rejeitados
        test_cases = [
            {
                'name': 'BTC/USDT (era rejeitado)',
                'coherence': 0.98,
                'momentum': 0.4,
                'expected': True  # Agora deve passar
            },
            {
                'name': 'SOL/USDT (era rejeitado)', 
                'coherence': 0.985,
                'momentum': 0.45,
                'expected': True  # Agora deve passar
            },
            {
                'name': 'XRP/USDT (era rejeitado)',
                'coherence': 0.99,
                'momentum': 0.35,
                'expected': True  # Agora deve passar
            },
            {
                'name': 'LDO/USDT (era rejeitado)',
                'coherence': 0.992,
                'momentum': 0.38,
                'expected': True  # Agora deve passar
            },
            {
                'name': 'Caso Extremo (deve ser rejeitado)',
                'coherence': 0.998,
                'momentum': 0.2,
                'expected': False  # Deve ser rejeitado
            }
        ]
        
        passed_count = 0
        for i, case in enumerate(test_cases):
            logger.info(f"\n📊 Caso {i+1}: {case['name']}")
            
            try:
                result = self.system._apply_coherence_cap(case['coherence'], case['momentum'])
                
                actual = result['approved']
                expected = case['expected']
                
                if actual == expected:
                    status = "✅ PASSOU"
                    passed_count += 1
                else:
                    status = "❌ FALHOU"
                
                logger.info(f"   {status}")
                logger.info(f"   Coherence: {case['coherence']:.3f}, Momentum: {case['momentum']:.2f}")
                logger.info(f"   Resultado: {result['reason']}")
                logger.info(f"   Esperado: {'Aprovado' if expected else 'Rejeitado'}, "
                           f"Obtido: {'Aprovado' if actual else 'Rejeitado'}")
                
            except Exception as e:
                logger.error(f"   ❌ Erro: {e}")
        
        if passed_count == len(test_cases):
            logger.info(f"\n✅ FILTRO COHERENCE CAP CORRIGIDO!")
            logger.info(f"   BTC, SOL, XRP, LDO agora passam corretamente")
            self.test_results['coherence_cap_fix'] = True
        else:
            logger.warning(f"\n⚠️ Correção parcial: {passed_count}/{len(test_cases)} casos")
    
    def test_gradual_adaptation(self):
        """Testa a nova lógica de adaptação gradual"""
        logger.info("\n🧪 TESTE 3: Adaptação Gradual de Thresholds")
        logger.info("=" * 60)
        
        # Simular diferentes taxas de aprovação
        test_scenarios = [
            {
                'name': 'Taxa muito baixa (4.8%)',
                'ema_rate': 0.048,
                'current_rate': 0.048,
                'expected_direction': 'relaxar'
            },
            {
                'name': 'Taxa muito alta (90.5%)',
                'ema_rate': 0.905,
                'current_rate': 0.905,
                'expected_direction': 'apertar'
            },
            {
                'name': 'Taxa ideal (20%)',
                'ema_rate': 0.20,
                'current_rate': 0.20,
                'expected_direction': None  # Não deve ajustar
            }
        ]
        
        passed_count = 0
        for i, scenario in enumerate(test_scenarios):
            logger.info(f"\n📊 Cenário {i+1}: {scenario['name']}")
            
            try:
                result = self.system._apply_gradual_threshold_adjustment(
                    scenario['ema_rate'], 
                    scenario['current_rate']
                )
                
                if scenario['expected_direction'] is None:
                    # Não deve aplicar ajuste
                    if not result['applied']:
                        logger.info(f"   ✅ Correto - não ajustou (taxa ideal)")
                        passed_count += 1
                    else:
                        logger.warning(f"   ⚠️ Ajustou quando não deveria")
                else:
                    # Deve aplicar ajuste na direção correta
                    if result['applied'] and result['direction'] == scenario['expected_direction']:
                        logger.info(f"   ✅ Correto - {result['direction']} thresholds")
                        logger.info(f"   📊 Fator: {result['adjustment_factor']:.3f}")
                        logger.info(f"   📝 {result['description']}")
                        passed_count += 1
                    else:
                        logger.warning(f"   ⚠️ Direção incorreta ou não aplicou")
                        
            except Exception as e:
                logger.error(f"   ❌ Erro: {e}")
        
        if passed_count == len(test_scenarios):
            logger.info(f"\n✅ ADAPTAÇÃO GRADUAL FUNCIONANDO!")
            logger.info(f"   Evita oscilações bruscas 90.5% ↔ 4.8%")
            self.test_results['gradual_adaptation'] = True
        else:
            logger.warning(f"\n⚠️ Funcionamento parcial: {passed_count}/{len(test_scenarios)} cenários")
    
    def test_system_stability(self):
        """Testa a estabilidade geral do sistema"""
        logger.info("\n🧪 TESTE 4: Estabilidade do Sistema")
        logger.info("=" * 60)
        
        try:
            # Verificar se componentes críticos estão funcionando
            checks = []
            
            # 1. Verificar inicialização
            if hasattr(self.system, 'empirical_calibrator'):
                checks.append(("Calibrador Empírico", True))
            else:
                checks.append(("Calibrador Empírico", False))
            
            # 2. Verificar filtros
            if hasattr(self.system, '_apply_coherence_cap'):
                checks.append(("Filtro Coherence Cap", True))
            else:
                checks.append(("Filtro Coherence Cap", False))
            
            # 3. Verificar adaptação gradual
            if hasattr(self.system, '_apply_gradual_threshold_adjustment'):
                checks.append(("Adaptação Gradual", True))
            else:
                checks.append(("Adaptação Gradual", False))
            
            # 4. Verificar geração de sinais
            try:
                test_metrics = {
                    'consciousness': 0.6,
                    'coherence': 0.65,
                    'confidence': 0.55,
                    'momentum': 0.8,
                    'predictive_score': 0.7,
                    'stability': 0.75
                }
                test_market = {'symbol': 'TEST/USDT', 'price': 100.0}
                
                signal = self.system.validate_trading_signal(test_market, test_metrics)
                checks.append(("Geração de Sinais", signal is not None))
            except Exception:
                checks.append(("Geração de Sinais", False))
            
            # Avaliar resultados
            passed_checks = sum(1 for _, status in checks if status)
            total_checks = len(checks)
            
            logger.info(f"\n📊 Verificações de Estabilidade:")
            for check_name, status in checks:
                status_icon = "✅" if status else "❌"
                logger.info(f"   {status_icon} {check_name}")
            
            if passed_checks == total_checks:
                logger.info(f"\n✅ SISTEMA ESTÁVEL!")
                logger.info(f"   Todas as {total_checks} verificações passaram")
                self.test_results['system_stability'] = True
            else:
                logger.warning(f"\n⚠️ Estabilidade parcial: {passed_checks}/{total_checks}")
                
        except Exception as e:
            logger.error(f"❌ Erro na verificação de estabilidade: {e}")
    
    def generate_final_report(self):
        """Gera relatório final das correções"""
        logger.info("\n🎉 RELATÓRIO FINAL - CORREÇÕES CRÍTICAS V4")
        logger.info("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        logger.info(f"📈 Testes Aprovados: {passed_tests}/{total_tests}")
        logger.info(f"📊 Taxa de Sucesso: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n📋 Detalhamento:")
        test_descriptions = {
            'bug_signal_quality': 'Correção Bug UnboundLocalError signal_quality',
            'coherence_cap_fix': 'Correção Filtro Coherence Cap (BTC, SOL, XRP, LDO)',
            'gradual_adaptation': 'Implementação Adaptação Gradual de Thresholds',
            'system_stability': 'Verificação Estabilidade Geral do Sistema'
        }
        
        for test_key, passed in self.test_results.items():
            status_icon = "✅" if passed else "❌"
            description = test_descriptions.get(test_key, test_key)
            logger.info(f"   {status_icon} {description}")
        
        if passed_tests == total_tests:
            logger.info("\n🚀 TODAS AS CORREÇÕES VALIDADAS!")
            logger.info("Sistema QUALIA pronto para operação estável")
            logger.info("\n🎯 Melhorias Implementadas:")
            logger.info("   • Bug crítico signal_quality corrigido")
            logger.info("   • BTC, SOL, XRP, LDO não são mais rejeitados incorretamente")
            logger.info("   • Adaptação gradual evita oscilações 90.5% ↔ 4.8%")
            logger.info("   • Sistema mantém estabilidade operacional")
        else:
            logger.warning("\n⚠️ Algumas correções precisam de ajustes adicionais")
            logger.warning("Revisar testes que falharam antes da produção")

def main():
    """Executa todos os testes de correções críticas"""
    logger.info("🚀 INICIANDO TESTES DAS CORREÇÕES CRÍTICAS V4")
    logger.info("Sistema QUALIA - Consciência Quântica YAA")
    logger.info("=" * 70)
    
    if not QUALIA_AVAILABLE:
        logger.error("❌ Sistema QUALIA não disponível")
        return
    
    try:
        tester = CorrecoesCriticasTester()
        
        # Executar todos os testes
        tester.test_signal_quality_bug_fix()
        tester.test_coherence_cap_fix()
        tester.test_gradual_adaptation()
        tester.test_system_stability()
        
        # Gerar relatório final
        tester.generate_final_report()
        
    except Exception as e:
        logger.error(f"❌ ERRO GERAL NOS TESTES: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
