#!/usr/bin/env python3
"""
QUALIA SUPERVISOR - Sistema de Múltiplas Instâncias Simultâneas
Gerencia 2 instâncias do QUALIA Binance com 21 minutos de diferença
"""

import subprocess
import time
import logging
import os
import sys
import signal
import psutil
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import threading
import json
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class QualiaInstance:
    """Representa uma instância do QUALIA"""
    process: subprocess.Popen
    start_time: datetime
    instance_id: int
    pid: int

class QualiaMultiInstanceSupervisor:
    """Supervisor para múltiplas instâncias simultâneas do QUALIA"""

    def __init__(self):
        self.script_path = "src/qualia/binance_corrected_system.py"
        self.instance_interval = 21 * 60  # 21 minutos em segundos
        self.max_instances = 2  # Máximo de 2 instâncias simultâneas
        self.active_instances: List[QualiaInstance] = []
        self.running = True
        self.next_instance_id = 1
        self.start_time = datetime.now()
        self.last_instance_start = None

        # Configurar logging
        self.setup_logging()

        # Verificar se o script existe
        if not os.path.exists(self.script_path):
            self.logger.error(f"❌ Script não encontrado: {self.script_path}")
            sys.exit(1)

        self.logger.info("🚀 QUALIA MULTI-INSTANCE SUPERVISOR INICIALIZADO")
        self.logger.info(f"📁 Script: {self.script_path}")
        self.logger.info(f"⏰ Intervalo entre instâncias: {self.instance_interval/60:.1f} minutos")
        self.logger.info(f"🔢 Máximo de instâncias simultâneas: {self.max_instances}")

    def setup_logging(self):
        """Configura sistema de logging"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Configurar logger
        self.logger = logging.getLogger("QualiaMultiSupervisor")
        self.logger.setLevel(logging.INFO)

        # Handler para arquivo
        log_file = log_dir / f"qualia_multi_supervisor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        # Handler para console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Formato
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def start_qualia_instance(self) -> bool:
        """Inicia uma nova instância do QUALIA"""
        try:
            # Verificar se já temos o máximo de instâncias
            if len(self.active_instances) >= self.max_instances:
                self.logger.warning(f"⚠️ Máximo de {self.max_instances} instâncias já atingido")
                return False

            instance_id = self.next_instance_id
            self.next_instance_id += 1

            self.logger.info(f"🚀 INICIANDO INSTÂNCIA {instance_id}")
            self.logger.info(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"📊 Instâncias ativas: {len(self.active_instances)}/{self.max_instances}")

            # Comando para executar o script
            cmd = [sys.executable, self.script_path]

            # Iniciar processo
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Criar instância
            instance = QualiaInstance(
                process=process,
                start_time=datetime.now(),
                instance_id=instance_id,
                pid=process.pid
            )

            # Adicionar à lista de instâncias ativas
            self.active_instances.append(instance)
            self.last_instance_start = datetime.now()

            self.logger.info(f"✅ Instância {instance_id} iniciada - PID: {process.pid}")

            # Iniciar thread de monitoramento para esta instância
            monitor_thread = threading.Thread(
                target=self.monitor_instance,
                args=(instance,),
                daemon=True
            )
            monitor_thread.start()

            return True

        except Exception as e:
            self.logger.error(f"❌ Erro ao iniciar instância QUALIA: {e}")
            return False

    def monitor_instance(self, instance: QualiaInstance):
        """Monitora uma instância específica do QUALIA"""
        try:
            self.logger.info(f"👁️ Iniciando monitoramento da instância {instance.instance_id}")

            # Aguardar conclusão do processo
            return_code = instance.process.wait()

            # Processo terminou
            uptime = datetime.now() - instance.start_time
            self.logger.warning(f"⚠️ Instância {instance.instance_id} terminou - Uptime: {uptime}")
            self.logger.info(f"📊 Return code: {return_code}")

            # Remover da lista de instâncias ativas
            self.remove_instance(instance)

        except Exception as e:
            self.logger.error(f"❌ Erro no monitoramento da instância {instance.instance_id}: {e}")
            self.remove_instance(instance)

    def remove_instance(self, instance: QualiaInstance):
        """Remove uma instância da lista de ativas"""
        try:
            if instance in self.active_instances:
                self.active_instances.remove(instance)
                self.logger.info(f"🗑️ Instância {instance.instance_id} removida da lista ativa")
                self.logger.info(f"📊 Instâncias restantes: {len(self.active_instances)}")
        except Exception as e:
            self.logger.error(f"❌ Erro ao remover instância: {e}")

    def terminate_instance(self, instance: QualiaInstance):
        """Termina uma instância específica"""
        try:
            if instance.process.poll() is None:
                # Tentar terminar graciosamente
                instance.process.terminate()

                # Aguardar até 10 segundos
                try:
                    instance.process.wait(timeout=10)
                    self.logger.info(f"✅ Instância {instance.instance_id} terminada graciosamente")
                except subprocess.TimeoutExpired:
                    # Forçar terminação
                    instance.process.kill()
                    instance.process.wait()
                    self.logger.warning(f"⚠️ Instância {instance.instance_id} terminada forçadamente")

        except Exception as e:
            self.logger.error(f"❌ Erro ao terminar instância {instance.instance_id}: {e}")

    def get_active_instances_count(self) -> int:
        """Retorna o número de instâncias ativas"""
        # Limpar instâncias mortas
        self.cleanup_dead_instances()
        return len(self.active_instances)

    def cleanup_dead_instances(self):
        """Remove instâncias mortas da lista"""
        dead_instances = []
        for instance in self.active_instances:
            if instance.process.poll() is not None:
                dead_instances.append(instance)

        for instance in dead_instances:
            self.remove_instance(instance)

    def should_start_new_instance(self) -> bool:
        """Determina se deve iniciar uma nova instância"""
        active_count = self.get_active_instances_count()

        # Se não há instâncias, sempre iniciar
        if active_count == 0:
            return True

        # Se já temos o máximo, não iniciar
        if active_count >= self.max_instances:
            return False

        # Se temos menos que o máximo, verificar se passou o intervalo
        if self.last_instance_start:
            time_since_last = datetime.now() - self.last_instance_start
            if time_since_last.total_seconds() >= self.instance_interval:
                return True

        return False

    def get_status_report(self):
        """Gera relatório de status"""
        uptime = datetime.now() - self.start_time

        # Informações das instâncias ativas
        instances_info = []
        for instance in self.active_instances:
            instance_uptime = datetime.now() - instance.start_time
            instances_info.append({
                "instance_id": instance.instance_id,
                "pid": instance.pid,
                "start_time": instance.start_time.isoformat(),
                "uptime": str(instance_uptime),
                "running": instance.process.poll() is None
            })

        status = {
            "supervisor_uptime": str(uptime),
            "active_instances_count": len(self.active_instances),
            "max_instances": self.max_instances,
            "instances": instances_info,
            "last_instance_start": self.last_instance_start.isoformat() if self.last_instance_start else None,
            "next_instance_id": self.next_instance_id,
            "instance_interval_minutes": self.instance_interval / 60,
            "timestamp": datetime.now().isoformat()
        }

        return status

    def save_status_report(self):
        """Salva relatório de status em arquivo"""
        try:
            status = self.get_status_report()
            status_file = f"qualia_multi_supervisor_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(status_file, 'w') as f:
                json.dump(status, f, indent=2)

            self.logger.info(f"📊 Status salvo: {status_file}")

        except Exception as e:
            self.logger.error(f"❌ Erro ao salvar status: {e}")

    def run(self):
        """Loop principal do supervisor"""
        self.logger.info("🚀 INICIANDO SUPERVISOR MULTI-INSTÂNCIA QUALIA")
        self.logger.info("=" * 70)
        self.logger.info("🎯 OBJETIVO: Manter 2 instâncias ativas com 21 min de diferença")
        self.logger.info("=" * 70)

        try:
            while self.running:
                # Verificar quantas instâncias estão ativas
                active_count = self.get_active_instances_count()

                self.logger.info(f"📊 Status atual: {active_count}/{self.max_instances} instâncias ativas")

                # Decidir se deve iniciar nova instância
                if self.should_start_new_instance():
                    self.logger.info("🎯 Condições atendidas para nova instância")
                    self.start_qualia_instance()
                else:
                    # Calcular tempo até próxima instância
                    if self.last_instance_start and active_count < self.max_instances:
                        time_since_last = datetime.now() - self.last_instance_start
                        time_remaining = self.instance_interval - time_since_last.total_seconds()
                        if time_remaining > 0:
                            self.logger.info(f"⏳ Próxima instância em {time_remaining/60:.1f} minutos")

                # Salvar relatório de status periodicamente
                self.save_status_report()

                # Aguardar antes da próxima verificação
                time.sleep(30)  # Verificar a cada 30 segundos

        except KeyboardInterrupt:
            self.logger.info("🛑 Interrupção pelo usuário")
        except Exception as e:
            self.logger.error(f"❌ Erro crítico no supervisor: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """Limpeza ao finalizar"""
        self.logger.info("🧹 Finalizando supervisor multi-instância...")
        self.running = False

        # Terminar todas as instâncias ativas
        for instance in self.active_instances.copy():
            self.logger.info(f"🛑 Terminando instância {instance.instance_id}")
            self.terminate_instance(instance)

        # Limpar lista
        self.active_instances.clear()

        # Salvar relatório final
        self.save_status_report()

        self.logger.info("✅ Supervisor multi-instância finalizado")

    def signal_handler(self, signum, frame):
        """Handler para sinais do sistema"""
        self.logger.info(f"📡 Sinal recebido: {signum}")
        self.running = False

def main():
    """Função principal"""
    print("🚀 QUALIA MULTI-INSTANCE SUPERVISOR")
    print("=" * 60)
    print("🎯 Gerencia 2 instâncias simultâneas do QUALIA Binance")
    print("⏰ Nova instância a cada 21 minutos")
    print("🔄 Monitoramento contínuo de múltiplas instâncias")
    print("📊 Logging detalhado de todas as atividades")
    print("🛑 Ctrl+C para parar")
    print("=" * 60)
    print("📋 LÓGICA DE OPERAÇÃO:")
    print("   1. Inicia primeira instância")
    print("   2. Aguarda 21 minutos")
    print("   3. Inicia segunda instância (primeira continua)")
    print("   4. Mantém 2 instâncias ativas simultaneamente")
    print("   5. Se alguma morrer, inicia nova após intervalo")
    print("=" * 60)

    # Criar e executar supervisor
    supervisor = QualiaMultiInstanceSupervisor()

    # Configurar handlers de sinal
    signal.signal(signal.SIGINT, supervisor.signal_handler)
    signal.signal(signal.SIGTERM, supervisor.signal_handler)

    # Executar
    supervisor.run()

if __name__ == "__main__":
    main()
